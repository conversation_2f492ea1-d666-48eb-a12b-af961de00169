# Hystrix超时配置建议
# 针对SmsTask中遇到的HystrixTimeoutException问题的配置建议

# 方案1: 在application.yml中添加以下配置
hystrix:
  command:
    # 全局默认配置
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 30000  # 30秒超时
    # 针对DeviceServiceClient的特定配置
    DeviceServiceClient#getIotCardByPage(IotCardQuery,String):
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000  # 60秒超时，因为查询大量数据可能需要更长时间
    # 针对SystemServiceClient的特定配置
    SystemServiceClient#getAccessToken(ClientInfo):
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 15000  # 15秒超时

# 方案2: Feign客户端超时配置
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 10000    # 连接超时10秒
        readTimeout: 30000       # 读取超时30秒
      device-service:            # 设备服务特定配置
        connectTimeout: 10000
        readTimeout: 60000       # 读取超时60秒
      system-service:            # 系统服务特定配置
        connectTimeout: 5000
        readTimeout: 15000

# 方案3: Ribbon负载均衡器超时配置
ribbon:
  ConnectTimeout: 10000          # 连接超时
  ReadTimeout: 30000             # 读取超时
  MaxAutoRetries: 1              # 同一服务器重试次数
  MaxAutoRetriesNextServer: 1    # 切换服务器重试次数

# 针对特定服务的Ribbon配置
device-service:
  ribbon:
    ConnectTimeout: 10000
    ReadTimeout: 60000
    MaxAutoRetries: 1
    MaxAutoRetriesNextServer: 1

system-service:
  ribbon:
    ConnectTimeout: 5000
    ReadTimeout: 15000
    MaxAutoRetries: 1
    MaxAutoRetriesNextServer: 1

# 方案4: 如果使用Spring Cloud Gateway，可以配置路由超时
spring:
  cloud:
    gateway:
      routes:
        - id: device-service
          uri: lb://device-service
          predicates:
            - Path=/device/**
          filters:
            - name: Hystrix
              args:
                name: device-service-fallback
                fallbackUri: forward:/fallback/device
        - id: system-service
          uri: lb://system-service
          predicates:
            - Path=/system/**
          filters:
            - name: Hystrix
              args:
                name: system-service-fallback
                fallbackUri: forward:/fallback/system

# 建议的配置优先级：
# 1. 首先尝试增加Hystrix超时时间（方案1）
# 2. 同时配置Feign客户端超时（方案2）
# 3. 确保Ribbon超时配置合理（方案3）
# 4. 监控和调优

# 注意事项：
# 1. Hystrix超时时间应该大于Feign客户端超时时间
# 2. Feign客户端超时时间应该大于Ribbon超时时间
# 3. 考虑网络延迟和服务响应时间
# 4. 定期监控和调整配置
