package com.bto.statistics.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.converter.vo.ElectricityInfoDTOMapper;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.AnalyzeObjectDataQueryDTO;
import com.bto.commons.pojo.dto.ElectricityStatisticsExportFileDTO;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import com.bto.commons.pojo.dto.PlantStatisticsExportFileDTO;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.ExcelUtils;
import com.bto.commons.utils.PlantStatisticsExcelCustomizeColumnWidth;
import com.bto.commons.utils.ReflectUtil;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.service.ExportFileService;
import com.github.pagehelper.PageInfo;
import com.google.common.io.Files;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 文件导出控制器
 * <AUTHOR>
 * @date 2023/5/6 14:28
 * @description 处理各类统计数据的Excel导出请求
 */
@RestController
@Api(tags = "文件导出模块")
@RequestMapping("export")
public class ExportFileController {
    @Autowired
    private ExportFileService exportFileService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private ElectricityInfoDTOMapper electricityInfoDTOMapper;

    /**
     * 导出能效收益统计Excel报表
     * @param query 查询条件
     * @param res HTTP响应
     * @return 文件字节数组响应实体
     * @throws IOException IO异常
     */
    //    @PostMapping(value = "/electricityStatistics", produces = "application/octet-stream")
//    @ApiOperation("能效收益统计-Excel报表导出")
    public ResponseEntity<byte[]> exportExcelElectricityStatistics(@RequestBody ElectricityStatisticsQueryDTO query, HttpServletResponse res) throws IOException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        File file = exportFileService.createElectricityStatisticsExcelFile(query, userInfo);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", StringUtils.newStringIso8859_1(StringUtils.getBytesUtf8(file.getName())));
        return new ResponseEntity<byte[]>(Files.toByteArray(file), headers, HttpStatus.CREATED);
    }

    /**
     * 导出分析对象数据Excel报表
     * @param query 查询条件
     * @param res HTTP响应
     * @return 文件字节数组响应实体
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    @PostMapping(value = "/getAnalyzeObjectData", produces = "application/octet-stream")
    @ApiOperation("分析对象数据-Excel报表导出")
    @OperateLog(module = "统计分析文件导出模块", operateName = "导出分析对象数据Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public ResponseEntity<byte[]> exportExcelAnalyzeObjectData(@RequestBody AnalyzeObjectDataQueryDTO query, HttpServletResponse res) throws IOException, IllegalAccessException {
        File file = exportFileService.createAnalyzeObjectDataExcelFile(query, query.getColumnList());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", StringUtils.newStringIso8859_1(StringUtils.getBytesUtf8(file.getName())));
        return new ResponseEntity<byte[]>(Files.toByteArray(file), headers, HttpStatus.CREATED);
    }

    /**
     * 导出电站统计Excel报表
     * @param query 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    @PostMapping(value = "getPlantStatisticsInfo", produces = "application/octet-stream")
    @ApiOperation(value = "电站统计-Excel报表导出")
    @OperateLog(module = "统计分析文件导出模块", operateName = "导出电站统计Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportExcelPlantStatistics(@RequestBody PlantStatisticsExportFileDTO query, HttpServletResponse response) throws IOException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<PlantStatisticsInfoVO> page = exportFileService.exportExcelPlantStatistics(userInfo, query);
        String fileName = "电站统计" + query.getPowerStartTime() + "~" + query.getPowerEndTime();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        try {
            // 内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentWriteCellStyle);
            EasyExcel.write(response.getOutputStream(), PlantStatisticsInfoVO.class).sheet("电站统计")
                    .registerWriteHandler(new PlantStatisticsExcelCustomizeColumnWidth())
                    .head(head(fileName))
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(page.getRecords());
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }

    /**
     * 导出电站月报统计Excel报表
     * @param query 查询条件
     */
    @PostMapping(value = "getPlantMonthStatement")
    @ApiOperation(value = "电站月报统计-Excel报表导出")
    @OperateLog(module = "统计分析文件导出模块", operateName = "导出电站月报统计Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportExcelPlantMonthStatement(@RequestBody PlantStatisticsExportFileDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantMonthStatementVO> list = exportFileService.exportExcelPlantMonthStatement(userInfo, query);
        ExcelUtils.excelExport(PlantMonthStatementVO.class, "电站日报" + query.getMonthly(), "当月每日发电量(kWh)汇总", list);
    }


    /**
     * 构建Excel表头
     * @param fileName 文件名
     * @return 表头列表
     */
    private List<List<String>> head(String fileName) {
        List<String> columnList = ReflectUtil.getFieldNameByObj(new PlantStatisticsInfoVO());
        List<List<String>> list = new ArrayList<>();
        for (String columnName : columnList) {
            List<String> head = new ArrayList<>();
            head.add(fileName);
            head.add(columnName);
            list.add(head);
        }
        return list;
    }



    /**
     * 导出能效收益统计Excel表格
     * @param query 查询条件
     * @param response HTTP响应
     */
    @PostMapping("getElectricityStatisticsInfo")
    @ApiOperation(value = "导出能效收益统计-区域/发电统计--Excel表格--hjw")
    @OperateLog(module = "统计分析文件导出模块", operateName = "导出能效收益统计Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportExcelElectricityStatisticsInfo(@RequestBody ElectricityStatisticsExportFileDTO query, HttpServletResponse response) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PageInfo<ElectricityStaticsInfoVO> pageInfo = exportFileService.exportExcelElectricityStatisticsInfo(query, userInfo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + "能效收益统计" + ".xlsx");
        List<ElectricityStaticsInfoVO> electricityStaticsInfoVOList = pageInfo.getList();
        ArrayList<ElectricityInfoWithPlantNameAndUidVO> electricityInfoWithPlantNameAndUidVOList = new ArrayList<>();
        for (ElectricityStaticsInfoVO electricityStaticsInfoVO : electricityStaticsInfoVOList) {
            List<ElectricityInfoVO> electricityList = electricityStaticsInfoVO.getElectricityList();
            // 将electricityStaticsInfoDTO的成员变量electricityList数据扁平化处理,ElectricityInfoDTO类 转成 ElectricityInfoWithPlantNameAndUidDTO 类
            for (ElectricityInfoVO electricityInfoVO : electricityList) {
                ElectricityInfoWithPlantNameAndUidVO electricityInfoWithPlantNameAndUidVO = electricityInfoDTOMapper.electricityInfoDTO2ElectricityInfoWithPlantNameAndUidDTO(electricityInfoVO);
                electricityInfoWithPlantNameAndUidVO.setPlantName(electricityStaticsInfoVO.getPlantName());
                electricityInfoWithPlantNameAndUidVO.setPlantUid(electricityStaticsInfoVO.getPlantUid());
                electricityInfoWithPlantNameAndUidVOList.add(electricityInfoWithPlantNameAndUidVO);
            }
        }
        try {
            EasyExcel.write(response.getOutputStream(), ElectricityInfoWithPlantNameAndUidVO.class).sheet("sheet1").doWrite(electricityInfoWithPlantNameAndUidVOList);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }

    /**
     * 导出项目巡检统计Excel
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param projectSpecial 项目专业
     * @param response HTTP响应
     * @throws Exception 异常
     */
    @PostMapping("exportProjectInspectionInfo")
    @ApiOperation(value = "导出项目巡检统计-excel")
    @OperateLog(module = "统计分析文件导出模块", operateName = "导出项目巡检统计Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportExcelElectricityStatisticsInfo(@RequestParam String startDate,
                                                     @RequestParam String endDate,
                                                     @RequestParam(required = false) String projectSpecial,
                                                     HttpServletResponse response) throws Exception {
        String fileName = java.net.URLEncoder.encode("导出项目巡检统计" + DateFormatUtils.format(org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -1), "yyyyMMdd") + ".xlsx", "UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        exportFileService.exportExcelElectricityStatisticsInfo(startDate,endDate,projectSpecial, response);
    }

}
