package com.bto.statistics.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.IntegrativeStatisticQueryDTO;
import com.bto.commons.pojo.vo.IntegrativeStatisticChartVO;
import com.bto.commons.pojo.vo.IntegrativeStatisticSheetVO;
import com.bto.commons.pojo.vo.ProjectInspectionInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.pojo.MonthData;
import com.bto.statistics.service.IntegrativeStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 综合统计控制器
 * 
 * 提供综合统计分析功能，包括图形数据查询、表格数据查询、项目巡检统计和全量数据导出等
 * 
 * <AUTHOR>
 * @date 2023/6/14 9:53
 */
@RestController
@RequestMapping("/integrativeStatistics")
@Api(tags = "综合统计模块")
public class IntegrativeStatisticController {
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private IntegrativeStatisticService integrativeStatisticService;

    /**
     * 查询综合统计图形数据
     * 
     * 根据查询条件获取综合统计的图形数据，用于图表展示和分析
     * 
     * @param query 综合统计查询条件
     * @return 综合统计图表数据列表
     */
    @PostMapping("integrativeStatisticChart")
    @ApiOperation("综合统计图形数据查询")
    public Result<List<IntegrativeStatisticChartVO>> integrativeStatisticChart(@RequestBody IntegrativeStatisticQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<IntegrativeStatisticChartVO> result = integrativeStatisticService.integrativeStatisticChart(userInfo, query);
        return Result.success(result);
    }

    /**
     * 查询综合统计表格数据
     * 
     * 根据查询条件获取综合统计的表格数据，用于详细数据展示和报表生成
     * 
     * @param query 综合统计查询条件
     * @return 综合统计表格数据对象
     * @throws Exception 当查询过程中发生异常时抛出
     */
    @PostMapping("integrativeStatisticSheet")
    @ApiOperation("综合统计表格数据查询")
    public Result<IntegrativeStatisticSheetVO> integrativeStatisticSheet(@RequestBody IntegrativeStatisticQueryDTO query) throws Exception {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IntegrativeStatisticSheetVO result = integrativeStatisticService.integrativeStatisticSheet(userInfo, query);
        return Result.success(result);
    }

    /**
     * 项目巡检统计
     * 
     * 根据日期范围和专业类型查询项目巡检统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param projectSpecial 项目专业类型，可选参数
     * @return 项目巡检统计信息列表
     */
    @PostMapping("/projectInspectionStatistics")
    @ApiOperation("项目巡检统计")
    public Result<List<ProjectInspectionInfoVO>> projectInspectionStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) String projectSpecial){
        List<ProjectInspectionInfoVO> result = integrativeStatisticService.projectInspectionStatistics(startDate,endDate,projectSpecial);
        return Result.success(result);
    }

    /**
     * 测试接口
     * 
     * 用于测试综合统计功能的内部接口，根据日期范围获取月度数据
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @return 月度数据列表
     */
    @PostMapping("/test")
    @ApiOperation("test")
    @OperateLog(module = "综合统计模块", operateName = "测试接口", type = OperateTypeEnum.REQUEST_OTHER)
    public Result<List<MonthData>> test(
            @RequestParam String start,
            @RequestParam String end
    ) {
        return Result.success(integrativeStatisticService.test(start, end));
    }
    /**
     * 导出全量统计数据
     * 
     * 根据指定条件和标准导出全量的综合统计数据为Excel文件
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @param realTownDayEfficiencyAvgStandard 实际乡镇日效率平均值标准，默认0.65
     * @param prePowerRateStandard 预功率比率标准，默认0.8
     * @param response HTTP响应对象，用于输出Excel文件
     */
    @PostMapping("/all")
    @ApiOperation("all")
    public void all(
            @RequestParam String start,
            @RequestParam String end,
            @RequestParam(required = false,defaultValue = "0.65") String realTownDayEfficiencyAvgStandard,
            @RequestParam(required = false,defaultValue = "0.8") String prePowerRateStandard,
            HttpServletResponse response
    ) {
        integrativeStatisticService.all(start, end,realTownDayEfficiencyAvgStandard,prePowerRateStandard,response);
    }

}
