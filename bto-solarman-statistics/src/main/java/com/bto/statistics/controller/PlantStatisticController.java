package com.bto.statistics.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.StatisticsQueryDTO;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.pojo.dto.PlantStatisticsQueryDTO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.pojo.LowPowerPlantVO;
import com.bto.statistics.service.PlantStatisticService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 电站统计控制器
 * 
 * 提供电站相关的统计信息查询功能，包括电站数量统计、发电量分析、低电量电站查询、
 * 能效收益统计、储能电站统计等全面的电站数据分析功能
 * 
 * <AUTHOR>
 * @date 2023/4/24 9:22
 */
@Api(tags = "电站统计信息模块")
@RestController
@RequestMapping("/plantStatistics")
public class PlantStatisticController {
    @Autowired
    private PlantStatisticService plantStatisticService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    /**
     * 获取电站统计数量信息
     * 
     * 查询当前用户权限范围内的电站数量统计信息，包括总电站数、运行中电站数、离网电站数等
     * 
     * @return 电站数量统计信息对象
     * @throws NoSuchFieldException 当字段不存在时抛出
     * @throws IllegalAccessException 当非法访问字段时抛出
     */
    @GetMapping("getPlantNumInfo")
    @ApiOperation(value = "获取电站统计数量信息", response = NumInfoDTO.class)
    @PreAuthorize(value = "hasAuthority('stats:plantNum:info')")
    public Result<NumInfoDTO> getPlantNumInfo() throws NoSuchFieldException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NumInfoDTO numInfo = plantStatisticService.getPlantNumInfo(userInfo);
        return Result.success(numInfo);
    }

    /**
     * 获取近七天发电量数据
     * 
     * 查询当前用户权限范围内所有电站近七天的发电量数据，用于短期趋势分析
     * 
     * @return 近七天发电量数据列表
     */
    @GetMapping("getElectricityBySevenDay")
    @ApiOperation(value = "获取近七天发电量数据")
    public Result getElectricityBySevenDay() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> getElectricityBySevenDay = plantStatisticService.getElectricityBySevenDay(userInfo);
        return Result.success(getElectricityBySevenDay);
    }

    /**
     * 按天数获取发电量数据
     * 
     * 根据指定天数获取发电量数据，支持灵活的时间段查询
     * 
     * @param dayNums 天数
     * @return 指定天数内的发电量数据列表
     */
    @GetMapping("getElectricityByNumDay/{dayNums}")
    @ApiOperation(value = "按天数获取发电量数据")
    public Result getElectricityByNumDay(@PathVariable Integer dayNums) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> getElectricityByNumDay = plantStatisticService.getElectricityByNumDay(userInfo, dayNums);
        return Result.success(getElectricityByNumDay);

    }

    /**
     * 获取近六个月发电量数据
     * 
     * 查询当前用户权限范围内所有电站近六个月的发电量数据，用于中长期趋势分析
     * 
     * @return 近六个月发电量数据列表
     */
    @GetMapping("getElectricityBySixMonth")
    @ApiOperation(value = "获取近六个月发电量数据")
    public Result getElectricityBySixMonth() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> electricityBySixMonth = plantStatisticService.getElectricityBySixMonth(userInfo);
        return Result.success(electricityBySixMonth);
    }

    /**
     * 获取电站发电量与工作效率数据
     * 
     * 获取电站统计数据与最大工作效率电站信息，包括总发电量、工作效率排名等
     * 
     * @return 电站发电量与工作效率数据对象
     */
    @GetMapping("getPlantElectricityInfo")
    @ApiOperation(value = "获取电站发电量与工作效率数据：包括电站统计数据与最大工作效率电站")
    @PreAuthorize(value = "hasAuthority('stats:plantElectricity:info')")
    public Result<PlantElectricityVO> getPlantElectricityInfo() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PlantElectricityVO plantElectricityInfo = plantStatisticService.getPlantElectricityInfo(userInfo);
        return Result.success(plantElectricityInfo);
    }

    /**
     * 获取电站统计数据信息
     * 
     * 根据查询条件获取电站统计数据信息，支持分页查询和条件筛选
     * 
     * @param query 电站统计查询条件
     * @return 分页的电站统计数据信息
     */
    @PostMapping("getPlantStatisticsInfo")
    @ApiOperation(value = "获取电站统计数据信息")
    public Result<Page<PlantStatisticsInfoVO>> getPlantStatisticsInfo(@RequestBody PlantStatisticsQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<PlantStatisticsInfoVO> result = plantStatisticService.getPlantStatisticsInfo(query,userInfo);
        return Result.success(result);
    }

    /**
     * 查询低电量电站
     * 
     * 根据查询条件获取低电量电站列表，用于识别发电量异常的电站
     * 
     * @param query 低电量电站查询条件
     * @return 低电量电站信息列表
     */
    @PostMapping("getLowPowerPlant")
    @ApiOperation(value = "低电量电站")
    public Result<List<LowPowerPlantVO>> getLowPowerPlant(@RequestBody LowPowerQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        return Result.success(plantStatisticService.getLowPowerPlant(query, userInfo));
    }

    /**
     * 导出低电量电站数据
     * 
     * 将低电量电站数据导出为Excel文件，便于离线分析和报告生成
     * 
     * @param query 低电量电站查询条件
     * @param response HTTP响应对象，用于输出Excel文件
     */
    @PostMapping("exportLowPowerPlant")
    @ApiOperation(value = "低电量电站导出")
    @OperateLog(module = "电站统计信息模块", operateName = "导出低电量电站Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportLowPowerPlant(@RequestBody LowPowerQuery query, HttpServletResponse response) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        plantStatisticService.exportLowPowerPlant(query, userInfo, response);
    }


    /**
     * 获取能效收益统计信息
     * 
     * 根据区域或发电类型进行能效收益统计分析，包括发电量、收益等关键指标
     * 
     * @param query 能效收益统计查询条件
     * @return 分页的能效收益统计信息
     */
    @PostMapping("getElectricityStatisticsInfo")
    @ApiOperation(value = "能效收益统计-区域/发电统计")
    public Result<PageInfo<ElectricityStaticsInfoVO>> getElectricityStatisticsInfo(@RequestBody ElectricityStatisticsQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PageInfo<ElectricityStaticsInfoVO> electricityStatisticsInfo = plantStatisticService.getElectricityStatisticsInfo(query, userInfo);
        return Result.success(electricityStatisticsInfo);
    }

    /**
     * 查询当日每小时发电量统计
     * 
     * 查询所有电站在当日内每个小时的发电量统计数据，用于日内发电趋势分析
     * 
     * @return 当日每小时发电量统计列表
     */
    @GetMapping("getEveryHourElectricityInfo")
    @ApiOperation(value = "查询所有电站当日每小时发电量统计")
    public Result getEveryHourElectricityInfo() { // todo 留待改造
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> everyHourElectricityInfo = plantStatisticService.getEveryHourElectricityInfo(userInfo);
        return Result.success(everyHourElectricityInfo);
    }

    /**
     * 获取电站等效小时数据分布统计
     * 
     * 统计分析各电站的等效利用小时数，评估电站的发电效率表现
     * 
     * @param query 电站统计查询条件
     * @param projectId 项目ID
     * @return 电站等效小时数分布统计结果
     */
    @PostMapping("getPlantEquivalentUseHourRate")
    @ApiOperation(value = "电站等效小时数据分布统计")
    public Result getPlantEquivalentUseHourRate(@RequestBody PlantStatisticsQueryDTO query, String projectId) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Map<String, Integer> result = plantStatisticService.getPlantEquivalentUseHour(query, userInfo, projectId);
        return Result.success(result);
    }

    /**
     * 获取储能电站统计信息
     * 
     * 专门用于储能电站的统计分析，包括储能容量、充放电效率、SOC状态等关键指标
     * 
     * @param queryDTO 储能电站统计查询条件
     * @return 储能电站统计信息结果
     */
    @PostMapping("getEnergyPlantStatistics")
    @ApiOperation(value = "获取储能电站统计信息")
    public Result<?> getStatistics(@Valid @RequestBody StatisticsQueryDTO queryDTO) {
        return plantStatisticService.queryStatistics(queryDTO);
    }
}
