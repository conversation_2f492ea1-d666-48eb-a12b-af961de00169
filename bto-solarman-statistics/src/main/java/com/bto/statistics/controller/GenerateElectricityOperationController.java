package com.bto.statistics.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.dto.AnalyzeObjectDataQueryDTO;
import com.bto.commons.pojo.dto.ElectricityAnalyzeDataDTO;
import com.bto.commons.pojo.dto.WeatherQueryDTO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.service.GenerateElectricityOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 发电运维控制器
 * 
 * 提供发电运维相关的统计分析功能，包括发电量分析、故障诊断、空间分析和天气数据查询等
 * 
 * <AUTHOR>
 * @date 2023/5/11 15:44
 */
@RestController
@RequestMapping("/generateElectricityOperation")
@Api(tags = "发电运维模块")
public class GenerateElectricityOperationController {
    @Autowired
    private GenerateElectricityOperationService generateElectricityOperationService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    /**
     * 查询分析对象数据
     * 
     * 根据条件查询分析对象数据和故障诊断对象数据，支持分页查询
     * 
     * @param query 分析对象数据查询条件
     * @return 分页的分析对象数据结果
     */
    @PostMapping("getAnalyzeObjectData")
    @ApiOperation(value = "根据条件查询分析对象数据&&故障诊断对象数据")
    public Result<Page<AnalyzeObjectDataVO>> getAnalyzeObjectData(@RequestBody AnalyzeObjectDataQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<AnalyzeObjectDataVO> result = generateElectricityOperationService.getAnalyzeObjectData(query);
        if (result.getTotal() > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    /**
     * 查询发电量分析数据
     * 
     * 根据条件查询日发电量数据图表，同时获取对应的天气数据，用于分析发电量与天气的关联性
     * 
     * @param query 发电量分析数据查询条件
     * @return 包含发电量结果和天气数据的Map集合
     */
    @PostMapping("getElectricityAnalyzeData")
    @ApiOperation(value = "根据条件查询日发电量数据图表")
    public Result getElectricityAnalyzeData(@RequestBody ElectricityAnalyzeDataDTO query) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<ChartElectricityInfoVO> electricityResult = generateElectricityOperationService.getElectricityAnalyzeData(query);
        List<WeatherInfoVO> weatherResult = generateElectricityOperationService.getWeatherAnalyzeData(query);
        resultMap.put("electricityResult", electricityResult);
        resultMap.put("weatherResult", weatherResult);
        if (electricityResult.size() > 0 || weatherResult.size() > 0) {
            return Result.success(resultMap);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "");
        }
    }

    /**
     * 查询空间分析数据
     * 
     * 根据日期时间和电站编号查询空间分析数据图表，用于空间维度的发电分析
     * 
     * @param dateTime 日期时间字符串
     * @param plantUid 电站唯一标识
     * @return 空间分析数据列表
     */
    @GetMapping("getSpaceAnalyzeData")
    @ApiOperation(value = "根据条件查空间分析数据图表")
    public Result getSpaceAnalyzeData(@RequestParam String dateTime, @RequestParam String plantUid) {
        List<SpaceAnalyzeDataVO> result = generateElectricityOperationService.getSpaceAnalyzeData(dateTime, plantUid);
        if (result.size() > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "");
        }
    }

    /**
     * 查询故障统计数据
     * 
     * 故障诊断模块专用接口，根据电站编号查询电站故障统计数据
     * 
     * @param plantUid 电站唯一标识
     * @return 故障统计数据列表
     */
    @GetMapping("getFaultStatisticsData/{plantUid}")
    @ApiOperation(value = "故障诊断-根据电站编号-查询电站故障统计数据")
    public Result getFaultStatisticsData(@PathVariable String plantUid) {
        List<FaultStatisticsDataVO> result = generateElectricityOperationService.getFaultStatisticsData(plantUid);
        if (result.size() > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    /**
     * 查询城市天气数据
     * 
     * 根据城市名称查询天气数据，用于发电量与环境因素的关联分析
     * 
     * @param query 天气数据查询条件，包含城市名称等信息
     * @return 天气信息数据列表
     */
    @PostMapping("getWeatherDataByCity")
    @ApiOperation(value = "根据城市名称-查询天气数据")
    public Result getWeatherDataByCity(@RequestBody WeatherQueryDTO query) {
        List<WeatherInfoVO> result = generateElectricityOperationService.getWeatherDataByCity(query);
        if (result.size() > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

}
