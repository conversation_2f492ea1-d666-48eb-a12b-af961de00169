package com.bto.statistics.controller;

import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.Result;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.service.AlarmStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * 告警统计控制器
 * <AUTHOR>
 * @date 2023/4/25 11:07
 * @description 处理告警统计相关请求
 */
@Api(tags = "告警统计信息模块")
@RestController
@RequestMapping("alarmStatistic")
public class AlarmStatisticController {
    @Autowired
    private AlarmStatisticService alarmStatisticService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    /**
     * 获取告警统计数量信息
     * @return 告警统计数量信息
     */
    @GetMapping("getAlarmNumInfo")
    @ApiOperation(value = "获取告警统计数量信息")
    @PreAuthorize(value = "hasAuthority('stats:alarmNum:info')")
    public Result getAlarmNumInfo(){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        HashMap<String, String> alarmNumInfo = alarmStatisticService.getAlarmNumInfo(userInfo);
        return Result.success(alarmNumInfo);
    }
}
