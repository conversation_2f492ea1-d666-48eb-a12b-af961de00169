package com.bto.statistics.controller;

import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.response.Result;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.service.InverterStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 逆变器统计控制器
 * 
 * 提供逆变器相关的统计信息查询功能，包括逆变器数量统计等
 * 
 * <AUTHOR>
 * @date 2023/4/25 9:45
 */
@Api(tags = "逆变器统计信息模块")
@RestController
@RequestMapping("inverterStatistic")
public class InverterStatisticController {
    @Autowired
    private InverterStatisticService inverterStatisticService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    /**
     * 获取逆变器统计数量信息
     * 
     * 查询当前用户权限范围内的逆变器数量统计信息，包括总数、在线数、离线数等
     * 
     * @return 逆变器数量统计信息对象
     * @throws NoSuchFieldException 当字段不存在时抛出
     * @throws IllegalAccessException 当非法访问字段时抛出
     */
    @GetMapping("getInverterNumInfo")
    @ApiOperation(value = "获取逆变器统计数量信息")
    @PreAuthorize(value = "hasAuthority('stats:inverterNum:info')")
    public Result<NumInfoDTO> getInverterNumInfo() throws NoSuchFieldException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NumInfoDTO numInfo = inverterStatisticService.getInverterNumInfo(userInfo);
        return Result.success(numInfo);
    }

}
