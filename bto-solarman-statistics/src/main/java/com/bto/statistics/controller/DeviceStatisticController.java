package com.bto.statistics.controller;

import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.response.Result;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.service.DeviceStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备统计控制器
 * <AUTHOR>
 * @date 2023/4/25 17:07
 * @description 处理设备统计相关请求
 */
@Api(tags = "设备统计信息模块")
@RestController
@RequestMapping("deviceStatistic")
public class DeviceStatisticController {
    @Autowired
    private DeviceStatisticService deviceStatisticService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    /**
     * 获取设备统计数量信息
     * @return 设备统计数量信息
     * @throws NoSuchFieldException 字段不存在异常
     * @throws IllegalAccessException 非法访问异常
     */
    @GetMapping("getDeviceNumInfo")
    @ApiOperation(value = "获取设备统计数量信息")
    @PreAuthorize(value = "hasAuthority('stats:deviceNum:info')")
    public Result<NumInfoDTO> getDeviceNumInfo() throws NoSuchFieldException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NumInfoDTO deviceNumInfo = deviceStatisticService.getDeviceNumInfo(userInfo);
        return Result.success(deviceNumInfo);
    }
}
