package com.bto.statistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.dto.AnalyzeObjectDataQueryDTO;
import com.bto.commons.pojo.dto.ElectricityAnalyzeDataDTO;
import com.bto.commons.pojo.dto.WeatherQueryDTO;

import java.util.List;

/**
 * 发电量运营分析服务接口
 * 提供发电量相关的运营分析功能，包括分析对象数据查询、发电量趋势分析、天气数据关联分析、空间分析等
 * 
 * <AUTHOR>
 * @date 2023/5/11 16:08
 */
public interface GenerateElectricityOperationService {

    /**
     * 根据条件查询分析对象数据
     * 
     * 分页查询分析对象数据，支持项目筛选、告警状态筛选、发电效率筛选等多种条件
     * 返回的数据包含电站基本信息、发电量、工作效率、发电效率、故障率等关键指标
     * 
     * @param query 分析对象数据查询条件，包含分页参数、筛选条件、排序规则等
     * @return 分页的分析对象数据列表，包含电站详细信息和各项统计指标
     */
    Page<AnalyzeObjectDataVO> getAnalyzeObjectData(AnalyzeObjectDataQueryDTO query);

    /**
     * 根据条件查询日发电量数据图表
     * 
     * 查询指定时间范围内的日发电量数据，用于生成发电量趋势图表
     * 支持按天查询，返回每日的发电量数据点
     * 
     * @param query 发电量分析查询条件，包含电站ID、时间范围等参数
     * @return 日发电量数据列表，每条数据包含日期和对应的发电量
     */
    List<ChartElectricityInfoVO> getElectricityAnalyzeData(ElectricityAnalyzeDataDTO query);

    /**
     * 根据条件查询日天气数据图表
     * 
     * 查询指定时间范围内的天气数据，包括温度、湿度、光照强度等气象指标
     * 天气数据与发电量数据关联分析，用于评估天气对发电效率的影响
     * 
     * @param query 发电量分析查询条件，包含电站ID、时间范围等参数
     * @return 天气数据列表，每条数据包含日期和对应的天气指标
     */
    List<WeatherInfoVO> getWeatherAnalyzeData(ElectricityAnalyzeDataDTO query);

    /**
     * 根据条件查空间分析数据图表
     * 
     * 查询指定日期和电站的空间分析数据，包括不同逆变器或组件的发电情况
     * 用于分析电站内部各组件的发电效率分布情况
     * 
     * @param dateTime 查询日期，格式：yyyy-MM-dd
     * @param plantUid 电站唯一标识
     * @return 空间分析数据列表，包含各组件的发电量、工作效率等指标
     */
    List<SpaceAnalyzeDataVO> getSpaceAnalyzeData(String dateTime, String plantUid);

    /**
     * 根据电站编号查询电站故障统计数据
     * 
     * 统计指定电站的故障情况，包括各类故障的发生次数和总故障数
     * 故障数据按故障类型分类统计，便于故障分析和预防
     * 
     * @param plantUid 电站唯一标识
     * @return 故障统计数据列表，包含各类故障的发生次数，最后一条为总故障数
     */
    List<FaultStatisticsDataVO> getFaultStatisticsData(String plantUid);

    /**
     * 根据城市查询天气数据
     * 
     * 查询指定城市在指定时间范围内的天气数据
     * 支持按城市和时间段筛选，用于区域性天气分析
     * 
     * @param query 天气查询条件，包含城市名称、开始时间、结束时间等参数
     * @return 城市天气数据列表，包含指定时间范围内的天气指标
     */
    List<WeatherInfoVO> getWeatherDataByCity(WeatherQueryDTO query);
}
