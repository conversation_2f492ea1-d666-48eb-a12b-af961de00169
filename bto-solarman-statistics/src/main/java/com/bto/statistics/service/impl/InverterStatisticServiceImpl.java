package com.bto.statistics.service.impl;

import com.bto.commons.constant.InverterStatusEnum;
import com.bto.commons.pojo.vo.NumInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.statistics.dao.InverterStatisticMapper;
import com.bto.statistics.service.InverterStatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 逆变器统计服务实现类
 * 实现逆变器相关数据统计的核心业务逻辑，包括逆变器状态分布统计、在线率计算、正常率计算等功能
 * 通过反射机制动态设置逆变器状态字段值，与设备统计服务实现类似的设计模式
 * 
 * <AUTHOR>
 * @date 2023/4/25 9:49
 */
@Service
public class InverterStatisticServiceImpl implements InverterStatisticService {
    @Autowired
    private InverterStatisticMapper inverterStatisticMapper;

    /**
     * 获取逆变器统计数量信息
     *
     * 查询当前用户权限范围内的逆变器统计信息
     *
     * @param userInfo 用户权限信息，包含用户ID、项目列表等权限控制信息
     * @return 逆变器统计信息DTO，包含各类逆变器状态的数量、在线率、正常率等统计指标
     * @throws NoSuchFieldException 当逆变器状态字段不存在时抛出
     * @throws IllegalAccessException 当逆变器状态字段访问受限时抛出
     */
    @Override
    public NumInfoDTO getInverterNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException {
        NumInfoDTO numInfo = new NumInfoDTO();
        List<NumInfoVO> inverterNumList = inverterStatisticMapper.getInverterNumInfo(userInfo);
        for (NumInfoVO infoDTO : inverterNumList) {
            String fieldName = InverterStatusEnum.getFieldNameByCode(infoDTO.getStatus());
            Field field = numInfo.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(numInfo, infoDTO.getStatusNum());
        }
        String totalNum = BusinessCalculateUtil.getTotalNum(numInfo);
        String onlineNum = BusinessCalculateUtil.getInverterOnlineNum(numInfo);
        String normalNum = BusinessCalculateUtil.getRealNormalNum(numInfo);
        numInfo.setTotalNum(totalNum);
        numInfo.setOnlineRate(onlineNum, totalNum);
        numInfo.setNormalRate(normalNum, totalNum);
        numInfo.setOnlineNum(onlineNum);
        numInfo.setNormalNum(normalNum);
        return numInfo;
    }
}
