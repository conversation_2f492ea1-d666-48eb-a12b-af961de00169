package com.bto.statistics.service;

import com.bto.commons.pojo.vo.RequireParamsDTO;

import java.util.HashMap;

/**
 * 告警统计服务接口
 * 提供告警相关数据的统计功能，包括告警电站数量、告警信息数量等核心指标的查询
 * 
 * <AUTHOR>
 * @date 2023/4/25 11:09
 */
public interface AlarmStatisticService {
    /**
     * 获取告警数量信息统计
     *
     * @param userInfo 用户权限信息，包含用户ID、项目列表等权限控制信息
     * @return 告警统计结果Map，key为统计类型("alarmPlantNum"-告警电站数, "alarmInfoNum"-告警信息数)，value为对应的字符串数值
     */
    HashMap<String, String> getAlarmNumInfo(RequireParamsDTO userInfo);
}
