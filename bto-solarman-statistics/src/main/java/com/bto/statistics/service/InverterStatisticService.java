package com.bto.statistics.service;

import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;

/**
 * 逆变器统计服务接口
 * 提供逆变器相关数据的统计分析功能，包括逆变器状态分布、在线率、正常率等关键指标的查询
 * 
 * <AUTHOR>
 * @date 2023/4/25 9:49
 */
public interface InverterStatisticService {

    /**
     * 获取逆变器统计数量信息
     * 
     * 查询当前用户权限范围内的逆变器统计信息
     *
     * @param userInfo 用户权限信息，包含用户ID、项目列表等权限控制信息
     * @return 逆变器统计信息DTO，包含各类逆变器状态的数量、在线率、正常率等统计指标
     * @throws NoSuchFieldException 当逆变器状态字段不存在时抛出
     * @throws IllegalAccessException 当逆变器状态字段访问受限时抛出
     */
    NumInfoDTO getInverterNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException;
}
