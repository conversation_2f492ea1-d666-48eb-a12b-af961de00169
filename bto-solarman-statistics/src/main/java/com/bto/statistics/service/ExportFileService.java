package com.bto.statistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.AnalyzeObjectDataQueryDTO;
import com.bto.commons.pojo.dto.ElectricityStatisticsExportFileDTO;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import com.bto.commons.pojo.dto.PlantStatisticsExportFileDTO;
import com.bto.commons.pojo.vo.ElectricityStaticsInfoVO;
import com.bto.commons.pojo.vo.PlantMonthStatementVO;
import com.bto.commons.pojo.vo.PlantStatisticsInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.github.pagehelper.PageInfo;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 文件导出服务接口
 * 提供各种统计数据导出功能，支持Excel格式导出，包括能效收益统计、分析对象数据、电站统计等多种导出类型
 * 
 * <AUTHOR>
 * @date 2023/5/6 14:34
 */
public interface ExportFileService {
    /**
     * 创建能效收益统计Excel文件
     * 
     * 根据查询条件生成能效收益统计的Excel文件，包含电站名称、发电量、收益等关键指标
     * 
     * @param query 电量统计查询条件，包含时间范围、电站筛选等参数
     * @param userInfo 用户权限信息，用于数据权限控制
     * @return 生成的Excel文件对象
     */
    File createElectricityStatisticsExcelFile(ElectricityStatisticsQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 创建分析对象数据Excel文件
     * 
     * 根据查询条件生成分析对象数据的Excel文件，支持自定义列配置
     * 
     * @param query 分析对象数据查询条件
     * @param columnList 自定义列配置列表，指定需要导出的数据列
     * @return 生成的Excel文件对象
     * @throws IllegalAccessException 当数据访问异常时抛出
     */
    File createAnalyzeObjectDataExcelFile(AnalyzeObjectDataQueryDTO query, List<String> columnList) throws IllegalAccessException;

    /**
     * 创建电站收益统计Excel文件
     * 
     * 根据查询条件生成电站收益统计的Excel文件，支持分页导出
     * 
     * @param userInfo 用户权限信息，用于数据权限控制
     * @param query 电站统计导出查询条件
     * @return 分页的电站统计信息列表
     * @throws IllegalAccessException 当数据访问异常时抛出
     */
    Page<PlantStatisticsInfoVO> exportExcelPlantStatistics(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) throws IllegalAccessException;

    /**
     * 导出Excel电量统计信息
     * 
     * 根据查询条件导出电量统计信息到Excel文件，支持分页查询
     * 
     * @param query 电量统计导出查询条件
     * @param userInfo 用户权限信息，用于数据权限控制
     * @return 分页的电量统计信息列表
     */
    PageInfo<ElectricityStaticsInfoVO> exportExcelElectricityStatisticsInfo(ElectricityStatisticsExportFileDTO query, RequireParamsDTO userInfo);

    /**
     * 导出项目巡检统计-excel
     * 
     * 根据时间范围和项目专项导出项目巡检统计数据到Excel文件，直接通过HTTP响应流输出
     * 
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param projectSpecial 项目专项ID，可为空
     * @param response HTTP响应对象，用于直接输出Excel文件流
     * @throws IOException 当文件操作异常时抛出
     */
    void exportExcelElectricityStatisticsInfo(String startDate, String endDate,String projectSpecial, HttpServletResponse response) throws IOException;

    /**
     * 获取电站统计文件流
     * 
     * 根据查询条件生成电站统计数据的二进制文件流，用于文件下载
     * 
     * @param query 电站统计查询条件，包含时间范围、筛选条件等参数
     * @return 电站统计数据的二进制文件流
     */
    byte[] getPlantFileInfo(PlantStatisticsExportFileDTO query);

    /**
     * 导出电站月度报表数据
     * 
     * 根据查询条件导出电站月度发电量和告警数据的综合报表
     * 
     * @param userInfo 用户权限信息，用于数据权限控制
     * @param query 电站统计导出查询条件
     * @return 电站月度报表数据列表，包含每日发电量和告警状态
     */
    List<PlantMonthStatementVO> exportExcelPlantMonthStatement(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query);
}