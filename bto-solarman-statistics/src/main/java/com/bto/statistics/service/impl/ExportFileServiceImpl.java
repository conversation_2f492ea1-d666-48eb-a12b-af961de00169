package com.bto.statistics.service.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.converter.vo.ElectricityStatisticsExportFileVOMapper;
import com.bto.commons.converter.vo.PlantStatisticsExportFileVOMapper;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.*;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.service.ExportFileService;
import com.bto.statistics.service.GenerateElectricityOperationService;
import com.bto.statistics.service.IntegrativeStatisticService;
import com.bto.statistics.service.PlantStatisticService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bto.commons.utils.CreateExcelUtils.createUserExcelFile;

/**
 * 文件导出服务实现类
 * 实现各类统计数据的Excel文件导出功能，包括能效收益统计、分析对象数据、电站统计等多种导出类型
 * 支持分页导出、自定义列配置、文件流输出等多种导出方式
 * 
 * <AUTHOR>
 * @date 2023/5/6 14:35
 */
@Service
public class ExportFileServiceImpl implements ExportFileService {
    @Autowired
    private PlantStatisticService plantStatisticService;
    @Autowired
    private GenerateElectricityOperationService generateElectricityOperationService;

    @Autowired
    private PlantStatisticsExportFileVOMapper plantStatisticsExportFileVOMapper;
    @Autowired
    private ElectricityStatisticsExportFileVOMapper electricityStatisticsExportFileVOMapper;
    @Autowired
    private IntegrativeStatisticService integrativeStatisticService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    /**
     * 创建能效收益统计Excel文件
     * 
     * 实现逻辑：
     * 1. 调用电站统计服务获取能效收益数据
     * 2. 使用工具类生成Excel文件
     * 3. 返回生成的文件对象
     * 
     * @param query 电量统计查询条件
     * @param userInfo 用户权限信息
     * @return 生成的Excel文件对象
     */
    @Override
    public File createElectricityStatisticsExcelFile(ElectricityStatisticsQueryDTO query, RequireParamsDTO userInfo) {
        PageInfo<ElectricityStaticsInfoVO> pageInfo = plantStatisticService.getElectricityStatisticsInfo(query, userInfo);
        return createUserExcelFile(pageInfo.getList());
    }

    /**
     * 创建分析对象数据Excel文件
     *
     * 根据查询条件生成分析对象数据的Excel文件，支持自定义列配置
     *
     * @param query 分析对象数据查询条件
     * @param columnList 自定义列配置列表，指定需要导出的数据列
     * @return 生成的Excel文件对象
     * @throws IllegalAccessException 当数据访问异常时抛出
     */
    @Override
    public File createAnalyzeObjectDataExcelFile(AnalyzeObjectDataQueryDTO query, List<String> columnList) throws IllegalAccessException {
        Page<AnalyzeObjectDataVO> result = generateElectricityOperationService.getAnalyzeObjectData(query);
        return CreateExcelUtils.createAnalyzeObjectDataExcelFile(result.getRecords(), columnList);
    }

    /**
     * 创建电站收益统计Excel文件
     *
     * 根据查询条件生成电站收益统计的Excel文件，支持分页导出
     *
     * @param userInfo 用户权限信息，用于数据权限控制
     * @param query 电站统计导出查询条件
     * @return 分页的电站统计信息列表
     * @throws IllegalAccessException 当数据访问异常时抛出
     */
    @Override
    public Page<PlantStatisticsInfoVO> exportExcelPlantStatistics(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) {
        if (!StringUtils.isNotBlank(query.getSheetName()) && !StringUtils.isNotEmpty(query.getSheetName())) {
            query.setSheetName("电站统计");
        }
        if ((!StringUtils.isNotBlank(query.getCurrentPage()) && !StringUtils.isNotEmpty(query.getCurrentPage())) && (!StringUtils.isNotBlank(query.getPageSize()) && !StringUtils.isNotEmpty(query.getPageSize()))) {
            query.setPageSize("1048576");
            query.setCurrentPage("1");
        }
        PlantStatisticsQueryDTO plantStatisticsQueryDTO = plantStatisticsExportFileVOMapper.plantStatisticsExportFileVO2PlantStatisticsQueryVO(query);
        return plantStatisticService.getPlantStatisticsInfo(plantStatisticsQueryDTO, userInfo);
    }

    /**
     * 导出Excel电量统计信息
     *
     * 根据查询条件导出电量统计信息到Excel文件，支持分页查询
     *
     * @param query 电量统计导出查询条件
     * @param userInfo 用户权限信息，用于数据权限控制
     * @return 分页的电量统计信息列表
     */
    @Override
    public PageInfo<ElectricityStaticsInfoVO> exportExcelElectricityStatisticsInfo(ElectricityStatisticsExportFileDTO query, RequireParamsDTO userInfo) {
        if (!StringUtils.isNotBlank(query.getSheetName()) && !StringUtils.isNotEmpty(query.getSheetName())) {
            query.setSheetName("能效收益统计");
        }
        if ((!StringUtils.isNotBlank(query.getCurrentPage()) && !StringUtils.isNotEmpty(query.getCurrentPage())) && (!StringUtils.isNotBlank(query.getPageSize()) && !StringUtils.isNotEmpty(query.getPageSize()))) {
            query.setPageSize("1048576");
            query.setCurrentPage("1");
        }
        ElectricityStatisticsQueryDTO electricityStatisticsQueryDTO = electricityStatisticsExportFileVOMapper.electricityStatisticsExportFileVO2ElectricityStatisticsQueryVO(query);
        return plantStatisticService.getElectricityStatisticsInfo(electricityStatisticsQueryDTO, userInfo);
    }

    /**
     * 导出项目巡检统计-excel
     *
     * 根据时间范围和项目专项导出项目巡检统计数据到Excel文件，直接通过HTTP响应流输出
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param projectSpecial 项目专项ID，可为空
     * @param response HTTP响应对象，用于直接输出Excel文件流
     * @throws IOException 当文件操作异常时抛出
     */
    @Override
    public void exportExcelElectricityStatisticsInfo(String startDate, String endDate, String projectSpecial, HttpServletResponse response) throws IOException {
        List<ProjectInspectionInfoVO> list = integrativeStatisticService.projectInspectionStatistics(startDate, endDate, projectSpecial);

        // 设置表头样式
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置表格内容样式
        WriteCellStyle bodyStyle = new WriteCellStyle();
        bodyStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        bodyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 合并单元格
        ExcelFillCellMergePrevCol mergePrevCol = new ExcelFillCellMergePrevCol();
        mergePrevCol.add(list.size(), 0, 2);
        // 拿到表格处理对象
        ExcelWriter writer = EasyExcel.write(response.getOutputStream())
                .needHead(true)
                .excelType(ExcelTypeEnum.XLSX)
                // 设置需要待合并的行和列。参数1：数值数组，指定需要合并的列；参数2：数值，指定从第几行开始合并
                .registerWriteHandler(new ExcelMergeCustomerCellUtils(new int[]{0, 1, 2}, 1))
                .registerWriteHandler(mergePrevCol)
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, bodyStyle))
                .build();
        // 设置表格sheet样式,并写入excel
        WriteSheet sheet = EasyExcel.writerSheet("ProjectInspectionInfo").head(ProjectInspectionInfoVO.class).sheetNo(1).build();

        writer.write(list, sheet);
        writer.finish();
    }

    /**
     * 获取电站统计文件流
     *
     * 根据查询条件生成电站统计数据的二进制文件流，用于文件下载
     *
     * @param query 电站统计查询条件，包含时间范围、筛选条件等参数
     * @return 电站统计数据的二进制文件流
     */
    @Override
    public byte[] getPlantFileInfo(PlantStatisticsExportFileDTO query) {
        Page<PlantStatisticsInfoVO> page = exportExcelPlantStatistics(query.getUserInfo(), query);
        String fileName = URLUtil.encode("电站统计" + query.getPowerStartTime() + "~" + query.getPowerEndTime()).replaceAll("\\+", "%20");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            // 内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentWriteCellStyle);

            ExcelWriter excelWriter = EasyExcel.write(outputStream, ElectricityInfoWithPlantNameAndUidVO.class)
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().registerWriteHandler(new PlantStatisticsExcelCustomizeColumnWidth()).head(head(fileName)).sheetName("电站统计信息").build();
            excelWriter.write(page.getRecords(), writeSheet);
            excelWriter.finish();
            excelWriter.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED);
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 导出电站月度报表数据
     *
     * 根据查询条件导出电站月度发电量和告警数据的综合报表
     *
     * @param userInfo 用户权限信息，用于数据权限控制
     * @param query 电站统计导出查询条件
     * @return 电站月度报表数据列表，包含每日发电量和告警状态
     */
    @Override
    public List<PlantMonthStatementVO> exportExcelPlantMonthStatement(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) {
        // 获取电站当月每日发电信息
        List<PlantMonthStatementVO> monthElectricity = integrativeStatisticService.getMonthElectricity(userInfo, query);
        List<PlantMonthStatementVO> alarmList = integrativeStatisticService.getMonthAlarm(userInfo, query);
        // List<PlantMonthStatementVO> combinedList = combineLists(monthElectricity, alarmList);

        Map<String, PlantMonthStatementVO> alarmMap = alarmList.stream()
                .collect(Collectors.toMap(PlantMonthStatementVO::getPlantUid, Function.identity()));

        // 遍历monthElectricity，并根据alarmMap更新状态
        monthElectricity.forEach(electricity -> {
            PlantMonthStatementVO alarm = alarmMap.get(electricity.getPlantUid());
            if (alarm != null) {
                updateElectricityStatus(electricity, alarm);
            }
        });

        return monthElectricity;

    }

    /**
     * 更新发电量对象的状态
     *
     * 根据告警对象的状态，更新发电量对象的状态
     *
     * @param electricity 发电量对象
     * @param alarm 告警对象
     */
    private void updateElectricityStatus(PlantMonthStatementVO electricity, PlantMonthStatementVO alarm) {
        // 使用反射来设置状态，避免硬编码字段名
        try {
            for (int day = 1; day <= 31; day++) {
                String fieldName = "day" + day + "Status";
                java.lang.reflect.Field field = PlantMonthStatementVO.class.getDeclaredField(fieldName);
                field.setAccessible(true);

                // 获取alarm对象中对应字段的值
                Object alarmFieldValue = field.get(alarm);
                field.set(electricity, alarmFieldValue);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("电站状态设置错误", e);
        }
    }

    /**
     * 构建Excel表头
     * @param fileName 文件名
     * @return 表头列表
     */
    private List<List<String>> head(String fileName) {
        List<String> columnList = ReflectUtil.getFieldNameByObj(new PlantStatisticsInfoVO());
        List<List<String>> list = new ArrayList<>();
        for (String columnName : columnList) {
            List<String> head = new ArrayList<>();
            head.add(fileName);
            head.add(columnName);
            list.add(head);
        }
        return list;
    }
}
