package com.bto.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.plantmanage.PlantServiceClient;
import com.bto.commons.constant.BusinessEnum;
import com.bto.commons.constant.ConditionEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.dto.AnalyzeObjectDataQueryDTO;
import com.bto.commons.pojo.dto.ElectricityAnalyzeDataDTO;
import com.bto.commons.pojo.dto.WeatherQueryDTO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.TableUtils;
import com.bto.commons.utils.TreeUtils;
import com.bto.statistics.dao.CommonMapper;
import com.bto.statistics.dao.GenerateElectricityOperationMapper;
import com.bto.statistics.dao.PlantStatisticsMapper;
import com.bto.statistics.service.GenerateElectricityOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 发电量运营分析服务实现类
 * 实现发电量相关的运营分析功能，包括分析对象数据查询、发电量趋势分析、天气数据关联分析、空间分析等复杂业务逻辑
 * 支持多维度数据筛选、树形结构处理、复杂计算和异常处理
 * 
 * <AUTHOR>
 * @date 2023/5/11 16:10
 */
@Service
public class GenerateElectricityOperationServiceImpl implements GenerateElectricityOperationService {
    @Autowired
    private GenerateElectricityOperationMapper generateElectricityOperationMapper;
    @Autowired
    private PlantStatisticsMapper plantStatisticsMapper;
    @Autowired
    private CommonMapper commonMapper;
    @Autowired
    private PlantServiceClient plantServiceClient;

    /**
     * 根据条件查询分析对象数据
     *
     * 分页查询分析对象数据，支持项目筛选、告警状态筛选、发电效率筛选等多种条件
     * 返回的数据包含电站基本信息、发电量、工作效率、发电效率、故障率等关键指标
     *
     * @param query 分析对象数据查询条件，包含分页参数、筛选条件、排序规则等
     * @return 分页的分析对象数据列表，包含电站详细信息和各项统计指标
     */
    @Override
    public Page<AnalyzeObjectDataVO> getAnalyzeObjectData(AnalyzeObjectDataQueryDTO query) {
        List<String> projectTypeList = query.getProjectTypeList();
            if (CollUtil.isNotEmpty(query.getProjectTypeList())) {
            //遍历传输的项目id，如果查询出其所有项目及其子项目
            List<ProjectInfoVO> allProject = generateElectricityOperationMapper.getProjectIdByPid(projectTypeList);
            //将所有项目构建为树形结构
            List<ProjectInfoVO> projectTree = TreeUtils.build(allProject);
            //获取该树的所有叶子节点
            List<ProjectInfoVO> allLeafNodeList = TreeUtils.getAllLeafNode(projectTree);
            //取出所有叶子节点中的项目id
            List<String> collect = allLeafNodeList.stream().map(ProjectInfoVO::getId).collect(Collectors.toList());
            query.setProjectTypeList(collect);
        }
        Page<AnalyzeObjectDataVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        Page<AnalyzeObjectDataVO> analyzeObjectData = new Page<>();
        if (query.getIsAlarm()!=null && !query.getIsAlarm()) {
            page.setSize(-1);
            page.setCurrent(-1);
//            analyzeObjectData = generateElectricityOperationMapper.getAnalyzeObjectData(invalidPage, query);
        }
        analyzeObjectData = generateElectricityOperationMapper.getAnalyzeObjectData(page, query);
        List<AnalyzeObjectDataVO> list = analyzeObjectData.getRecords();
        List<String> plantUidArray = list.stream().map(AnalyzeObjectDataVO::getPlantUid).collect(Collectors.toList());
        List<PlantStatisticsInfoVO> plantAlarmNum = plantStatisticsMapper.getPlantAlarmNum(null, plantUidArray, "");
        for (int i = 0; i < list.size(); i++) {
            AnalyzeObjectDataVO o = list.get(i);
            BigDecimal power = new BigDecimal(o.getPower());
            BigDecimal todayElectricity = new BigDecimal(o.getTodayElectricity());
            BigDecimal plantCapacity = new BigDecimal(o.getPlantCapacity());
            //工作效率 = (power/plant_capacity)*100%          -- 保留两位
            BigDecimal workEfficiency = power.divide(plantCapacity, BigDecimal.ROUND_HALF_UP).setScale(2);
            o.setWorkEfficiency(workEfficiency + "%");
            //发电效率 = （electricity/(3.5*装机容量)）
            BigDecimal electricityEfficiency = todayElectricity.divide(plantCapacity.multiply(BigDecimal.valueOf(3.5)), BigDecimal.ROUND_HALF_UP).setScale(2);
            o.setElectricityEfficiency(electricityEfficiency + "%");
            //故障率=故障数/运行天数
            Date date = DateUtil.parse(o.getCreateTime());
            Date nowDate = DateUtil.date();
            long between = DateUtil.between(date, nowDate, DateUnit.DAY);
            if (between != 0) {
                BigDecimal alarmNum = new BigDecimal(plantAlarmNum.get(i).getAlarmNum());
                BigDecimal failureRate = alarmNum.divide(BigDecimal.valueOf(between), BigDecimal.ROUND_HALF_UP).setScale(2);
                o.setFailureRate(failureRate + "%");
            } else {
                o.setFailureRate("0.00%");
            }
            list.set(i, o);
        }
        if (query.getIsAlarm()!=null && !query.getIsAlarm()) {
            if (query.getFilterType().equals(BusinessEnum.ELECTRICITY.getColumn())) {
                if (query.getReferenceType().equals(BusinessEnum.MAX_VALUE.getColumn())) {
                    BigDecimal maxValue = list.stream().map(analyzeObjectDataDTO -> {
                        return new BigDecimal(analyzeObjectDataDTO.getTodayElectricity());
                    }).reduce(BigDecimal.ZERO, BigDecimal::max);
                    list = list.stream().filter(analyzeObjectDataDTO -> {
                                BigDecimal todayElectricity = new BigDecimal(analyzeObjectDataDTO.getTodayElectricity());
                                int flag = todayElectricity.compareTo(maxValue.multiply(new BigDecimal(query.getReferenceValue())));
                                if (flag <= 0) {
                                    return true;
                                }
                                return false;
                            }
                    ).collect(Collectors.toList());
                    List<AnalyzeObjectDataVO> list1 = new ArrayList<AnalyzeObjectDataVO>();
                    int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
                    int endIndex = startIndex + query.getPageSize();
                    if(list.size()<endIndex){
                        endIndex = list.size();
                    }
                    for (int i = startIndex; i < endIndex; i++) {
                        list1.add(list.get(i));
                    }
                    analyzeObjectData.setRecords(list1);
                    analyzeObjectData.setTotal(list.size());
                    analyzeObjectData.setCurrent(query.getCurrentPage());
                    analyzeObjectData.setSize(query.getPageSize());
                    return analyzeObjectData;
                } else if (query.getReferenceType().equals(BusinessEnum.AVG_VALUE.getColumn())) {
                    BigDecimal avgValue = list.stream().map(analyzeObjectDataDTO -> {
                        return new BigDecimal(analyzeObjectDataDTO.getTodayElectricity());
                    }).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
                    list = list.stream().filter(analyzeObjectDataDTO -> {
                                BigDecimal todayElectricity = new BigDecimal(analyzeObjectDataDTO.getTodayElectricity());
                                int flag = todayElectricity.compareTo(avgValue.multiply(new BigDecimal(query.getReferenceValue())));
                                if (flag <= 0) {
                                    return true;
                                }
                                return false;
                            }
                    ).collect(Collectors.toList());
                    List<AnalyzeObjectDataVO> list1 = new ArrayList<AnalyzeObjectDataVO>();
                    int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
                    int endIndex = startIndex + query.getPageSize();
                    if(list.size()<endIndex){
                        endIndex = list.size();
                    }
                    for (int i = startIndex; i < endIndex; i++) {
                        list1.add(list.get(i));
                    }
                    analyzeObjectData.setRecords(list1);
                    analyzeObjectData.setTotal(list.size());
                    analyzeObjectData.setCurrent(query.getCurrentPage());
                    analyzeObjectData.setSize(query.getPageSize());
                    return analyzeObjectData;
                } else {
                    throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR.getCode(),
                            ResultEnum.REQUESTPARAM_ERROR.getMessage());
                }
            } else if (query.getFilterType().equals(BusinessEnum.WORKEFFICIENCY.getColumn())) {
                if (query.getReferenceType().equals(BusinessEnum.MAX_VALUE.getColumn())) {
                    BigDecimal maxValue = list.stream().map(analyzeObjectDataDTO -> {
                        return new BigDecimal(analyzeObjectDataDTO.getWorkEfficiency().replace("%", ""));
                    }).reduce(BigDecimal.ZERO, BigDecimal::max);
                    list = list.stream().filter(analyzeObjectDataDTO -> {
                                BigDecimal workEfficiency = new BigDecimal(analyzeObjectDataDTO.getWorkEfficiency().replace("%", ""));
                                int flag = workEfficiency.compareTo(maxValue.multiply(new BigDecimal(query.getReferenceValue())));
                                if (flag <= 0) {
                                    return true;
                                }
                                return false;
                            }
                    ).collect(Collectors.toList());
                    List<AnalyzeObjectDataVO> list1 = new ArrayList<AnalyzeObjectDataVO>();
                    int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
                    int endIndex = startIndex + query.getPageSize();
                    if(list.size()<endIndex){
                        endIndex = list.size();
                    }
                    for (int i = startIndex; i < endIndex; i++) {
                        list1.add(list.get(i));
                    }
                    analyzeObjectData.setRecords(list1);
                    analyzeObjectData.setTotal(list.size());
                    analyzeObjectData.setCurrent(query.getCurrentPage());
                    analyzeObjectData.setSize(query.getPageSize());
                    return analyzeObjectData;
                } else if (query.getReferenceType().equals(BusinessEnum.AVG_VALUE.getColumn())) {
                    BigDecimal avgValue = list.stream().map(analyzeObjectDataDTO -> {
                        return new BigDecimal(analyzeObjectDataDTO.getWorkEfficiency().replace("%", ""));
                    }).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
                    list = list.stream().filter(analyzeObjectDataDTO -> {
                                BigDecimal workEfficiency = new BigDecimal(analyzeObjectDataDTO.getWorkEfficiency().replace("%", ""));
                                int flag = workEfficiency.compareTo(avgValue.multiply(new BigDecimal(query.getReferenceValue())));
                                if (flag <= 0) {
                                    return true;
                                }
                                return false;
                            }
                    ).collect(Collectors.toList());
                    List<AnalyzeObjectDataVO> list1 = new ArrayList<AnalyzeObjectDataVO>();
                    int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
                    int endIndex = startIndex + query.getPageSize();
                    if(list.size()<endIndex){
                        endIndex = list.size();
                    }
                    for (int i = startIndex; i < endIndex; i++) {
                        list1.add(list.get(i));
                    }
                    analyzeObjectData.setRecords(list1);
                    analyzeObjectData.setTotal(list.size());
                    analyzeObjectData.setCurrent(query.getCurrentPage());
                    analyzeObjectData.setSize(query.getPageSize());
                    return analyzeObjectData;
                } else {
                    throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR.getCode(),
                            ResultEnum.REQUESTPARAM_ERROR.getMessage());
                }
            } else if (query.getFilterType().equals(BusinessEnum.FAILURERATE.getColumn())) {
                if (query.getReferenceType().equals(BusinessEnum.MAX_VALUE.getColumn())) {
                        BigDecimal maxValue = list.stream().map(analyzeObjectDataDTO -> {
                            return new BigDecimal(analyzeObjectDataDTO.getFailureRate().replace("%", ""));
                        }).reduce(BigDecimal.ZERO, BigDecimal::max);
                        list = list.stream().filter(analyzeObjectDataDTO -> {
                                    BigDecimal failureRate = new BigDecimal(analyzeObjectDataDTO.getFailureRate().replace("%", ""));
                                    int flag = failureRate.compareTo(maxValue.multiply(new BigDecimal(query.getReferenceValue())));
                                    if (flag <= 0) {
                                        return true;
                                    }
                                    return false;
                                }
                        ).collect(Collectors.toList());
                        List<AnalyzeObjectDataVO> list1 = new ArrayList<AnalyzeObjectDataVO>();
                    int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
                    int endIndex = startIndex + query.getPageSize();
                    if(list.size()<endIndex){
                        endIndex = list.size();
                    }
                        for (int i = startIndex; i < endIndex; i++) {
                            list1.add(list.get(i));
                        }
                        analyzeObjectData.setRecords(list1);
                        analyzeObjectData.setTotal(list.size());
                    analyzeObjectData.setCurrent(query.getCurrentPage());
                    analyzeObjectData.setSize(query.getPageSize());
                        return analyzeObjectData;
                    } else if (query.getReferenceType().equals(BusinessEnum.AVG_VALUE.getColumn())) {
                        BigDecimal avgValue = list.stream().map(analyzeObjectDataDTO -> {
                            return new BigDecimal(analyzeObjectDataDTO.getFailureRate().replace("%", ""));
                        }).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
                        list = list.stream().filter(analyzeObjectDataDTO -> {
                                    BigDecimal failureRate = new BigDecimal(analyzeObjectDataDTO.getFailureRate().replace("%", ""));
                                    int flag = failureRate.compareTo(avgValue.multiply(new BigDecimal(query.getReferenceValue())));
                                    if (flag <= 0) {
                                        return true;
                                    }
                                    return false;
                                }
                        ).collect(Collectors.toList());
                        List<AnalyzeObjectDataVO> list1 = new ArrayList<AnalyzeObjectDataVO>();
                    int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
                    int endIndex = startIndex + query.getPageSize();
                    if(list.size()<endIndex){
                        endIndex = list.size();
                    }
                        for (int i = startIndex; i < endIndex; i++) {
                            list1.add(list.get(i));
                        }
                        analyzeObjectData.setRecords(list1);
                        analyzeObjectData.setTotal(list.size());
                    analyzeObjectData.setCurrent(query.getCurrentPage());
                    analyzeObjectData.setSize(query.getPageSize());
                        return analyzeObjectData;
                    } else {
                        throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR.getCode(),
                                ResultEnum.REQUESTPARAM_ERROR.getMessage());
                    }
                } else {
                    throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR.getCode(),
                            ResultEnum.REQUESTPARAM_ERROR.getMessage());
                }
            }
        analyzeObjectData.setRecords(list);
//        analyzeObjectData.setTotal(list.size());
        return analyzeObjectData;
    }

    /**
     * 根据条件查询日发电量数据图表
     *
     * 查询指定时间范围内的日发电量数据，用于生成发电量趋势图表
     * 支持按天查询，返回每日的发电量数据点
     *
     * @param query 发电量分析查询条件，包含电站ID、时间范围等参数
     * @return 日发电量数据列表，每条数据包含日期和对应的发电量
     */
    @Override
    public List<ChartElectricityInfoVO> getElectricityAnalyzeData(ElectricityAnalyzeDataDTO query) {
        if (query.getStartTime().length() == ConditionEnum.DATE_LENGTH_OF_DAY.getLength()
                && query.getEndTime().length() == ConditionEnum.DATE_LENGTH_OF_DAY.getLength()) {
            List<ChartElectricityInfoVO> result = plantStatisticsMapper.getElectricityAnalyzeDataByDay(query);
            return result;
        } else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED.getCode(),
                    ResultEnum.DATETIME_FORMAT_FAILED.getMessage());
        }
    }

    /**
     * 根据条件查询日天气数据图表
     *
     * 查询指定时间范围内的天气数据，包括温度、湿度、光照强度等气象指标
     * 天气数据与发电量数据关联分析，用于评估天气对发电效率的影响
     *
     * @param query 发电量分析查询条件，包含电站ID、时间范围等参数
     * @return 天气数据列表，每条数据包含日期和对应的天气指标
     */
    @Override
    public List<WeatherInfoVO> getWeatherAnalyzeData(ElectricityAnalyzeDataDTO query) {
        //查询电站信息
        Result plantInfo = plantServiceClient.getPlantInfoByPlantUid(query.getPlantUid());
        if (ResultEnum.SUCCESS.getCode().equals(plantInfo.getStatus())) {
            List<WeatherInfoVO> result = new ArrayList<>();
            Plant plant = JSONObject.parseObject(JSON.toJSONString(plantInfo.getData()), Plant.class);
            query.getPlantUid();
            final String TABLE_NAME_PREFIX = "bto_weather.bto_weather_";
            List<String> tableNameList = TableUtils.getTableSuffix(TABLE_NAME_PREFIX, query.getStartTime(), query.getEndTime());
            for (String tableName : tableNameList) {
                if (checkTableExists(tableName)) {
                    List<WeatherInfoVO> weatherAnalyzeData = plantStatisticsMapper.getWeatherAnalyzeData(tableName, query.getStartTime(), query.getEndTime(), plant.getCity());
                    result.addAll(weatherAnalyzeData);
                }
            }
            return result;
        } else {
            throw new BusinessException(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage());
        }
    }

    /**
     * 根据条件查空间分析数据图表
     *
     * 查询指定日期和电站的空间分析数据，包括不同逆变器或组件的发电情况
     * 用于分析电站内部各组件的发电效率分布情况
     *
     * @param dateTime 查询日期，格式：yyyy-MM-dd
     * @param plantUid 电站唯一标识
     * @return 空间分析数据列表，包含各组件的发电量、工作效率等指标
     */
    @Override
    public List<SpaceAnalyzeDataVO> getSpaceAnalyzeData(String dateTime, String plantUid) {
        final String TABLE_PREFIX = "bto_inverter_";
        String tableName = TableUtils.getTableName(TABLE_PREFIX, dateTime);
        List<SpaceAnalyzeDataVO> spaceAnalyzeDataList = plantStatisticsMapper.getSpaceAnalyzeData(tableName, plantUid);
        spaceAnalyzeDataList.forEach(spaceAnalyzeData -> {
            BigDecimal plantCapacity = new BigDecimal(spaceAnalyzeData.getPlantCapacity());
            BigDecimal totalPower = new BigDecimal(spaceAnalyzeData.getTotalPower());
            BigDecimal workEfficiency = totalPower.multiply(BigDecimal.valueOf(100)).divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP);
            spaceAnalyzeData.setWorkEfficiency(workEfficiency.toString() + "%");
        });
        return spaceAnalyzeDataList;
    }

    /**
     * 根据电站编号查询电站故障统计数据
     *
     * 统计指定电站的故障情况，包括各类故障的发生次数和总故障数
     * 故障数据按故障类型分类统计，便于故障分析和预防
     *
     * @param plantUid 电站唯一标识
     * @return 故障统计数据列表，包含各类故障的发生次数，最后一条为总故障数
     */
    @Override
    public  List<FaultStatisticsDataVO> getFaultStatisticsData(String plantUid) {
        List<FaultStatisticsDataVO> faultStatisticsData = plantStatisticsMapper.getFaultStatisticsData(plantUid);
        if (faultStatisticsData.size()>0){
            Integer totalNums = faultStatisticsData.stream().mapToInt(FaultStatisticsDataVO::getNums).sum();
            faultStatisticsData.add(new FaultStatisticsDataVO(plantUid,"total",totalNums));
            return faultStatisticsData;
        }else {
            faultStatisticsData.add(new FaultStatisticsDataVO(plantUid,null,0));
            return faultStatisticsData;
        }
    }

    /**
     * 根据城市查询天气数据
     *
     * 查询指定城市在指定时间范围内的天气数据
     * 支持按城市和时间段筛选，用于区域性天气分析
     *
     * @param query 天气查询条件，包含城市名称、开始时间、结束时间等参数
     * @return 城市天气数据列表，包含指定时间范围内的天气指标
     */
    @Override
    public List<WeatherInfoVO> getWeatherDataByCity(WeatherQueryDTO query) {
        List<WeatherInfoVO> result = new ArrayList<>();
        final String TABLE_NAME_PREFIX = "bto_weather.bto_weather_";
        List<String> tableNameList = TableUtils.getTableSuffix(TABLE_NAME_PREFIX, query.getStartTime(), query.getEndTime());
        for (String tableName : tableNameList) {
            if (checkTableExists(tableName)) {
                List<WeatherInfoVO> weatherAnalyzeData = plantStatisticsMapper.getWeatherAnalyzeData(tableName, query.getStartTime().concat(" 00:00:00"), query.getEndTime().concat(" 23:59:59"), query.getCity());
                result.addAll(weatherAnalyzeData);
            }
        }
        return result;
    }

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 表存在返回true，不存在返回false
     */
    public boolean checkTableExists(String tableName) {
        String[] split = tableName.split("\\.");
        final String TABLE_SCHEMA = split[0];
        try {
            Integer count = commonMapper.checkTableExistsWithSchema(TABLE_SCHEMA, split[1]);
            return count == 1;
        } catch (Exception e) {
            Map<String, String> list = commonMapper.checkTableExistsWithShow(tableName);
            if (!CollectionUtils.isEmpty(list)) {
                return true;
            }
        }
        return false;
    }


}

