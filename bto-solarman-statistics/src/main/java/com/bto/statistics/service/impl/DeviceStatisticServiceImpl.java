package com.bto.statistics.service.impl;

import com.bto.commons.pojo.vo.NumInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.constant.DeviceStatus;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.statistics.dao.DeviceStatisticMapper;
import com.bto.statistics.service.DeviceStatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 设备统计服务实现类
 * 实现设备相关数据统计的核心业务逻辑，包括设备状态分布统计、在线率计算、正常率计算等功能
 * 通过反射机制动态设置设备状态字段值，确保代码的可扩展性和维护性
 * 
 * <AUTHOR>
 * @date 2023/4/25 17:34
 */
@Service
public class DeviceStatisticServiceImpl implements DeviceStatisticService {
    
    @Autowired
    private DeviceStatisticMapper deviceStatisticMapper;

    /**
     * 获取设备统计数量信息
     *
     * @param userInfo 用户权限信息，包含用户ID、项目列表等权限控制信息
     * @return 设备统计信息DTO，包含设备总数、各类状态数量、在线率、正常率等指标
     * @throws NoSuchFieldException 当设备状态对应的字段不存在时抛出
     * @throws IllegalAccessException 当设备状态字段访问受限时抛出
     */
    @Override
    public NumInfoDTO getDeviceNumInfo(RequireParamsDTO userInfo ) throws NoSuchFieldException, IllegalAccessException {
        //查询设备各状态的数量
        List<NumInfoVO> deviceStatusNumList = deviceStatisticMapper.getDeviceStatusNumInfo(userInfo);
        NumInfoDTO numInfo = new NumInfoDTO();
        for (NumInfoVO infoDTO : deviceStatusNumList) {
            String fieldName = DeviceStatus.getFieldNameByCode(infoDTO.getStatus());
            Field field = numInfo.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(numInfo, infoDTO.getStatusNum());
        }
        String totalNum = BusinessCalculateUtil.getTotalNum(numInfo);
        String onlineNum = BusinessCalculateUtil.getInverterOnlineNum(numInfo);
        String normalNum = BusinessCalculateUtil.getRealNormalNum(numInfo);
        String offlineNum = BusinessCalculateUtil.getRealOfflineNum(numInfo);
        numInfo.setTotalNum(totalNum);
        numInfo.setOnlineRate(onlineNum, totalNum);
        numInfo.setNormalRate(normalNum, totalNum);
        numInfo.setOnlineNum(onlineNum);
        numInfo.setNormalNum(normalNum);
        numInfo.setOfflineNum(offlineNum);
        return numInfo;
    }
}
