package com.bto.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bto.api.feign.alarm.AlarmServiceClient;
import com.bto.api.feign.plantmanage.PlantServiceClient;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.constant.Constant;
import com.bto.commons.constant.DeviceStatusField;
import com.bto.commons.constant.RedisKey;
import com.bto.commons.constant.TableNameConstant;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.HourTownQuery;
import com.bto.commons.pojo.dto.IntegrativeStatisticQueryDTO;
import com.bto.commons.pojo.dto.PlantStatisticsExportFileDTO;
import com.bto.commons.pojo.entity.HourTownEntity;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.*;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.dao.IntegrativeStatisticMapper;
import com.bto.statistics.pojo.AllData;
import com.bto.statistics.pojo.MonthData;
import com.bto.statistics.service.IntegrativeStatisticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 综合统计服务实现类
 * 实现综合性的数据统计分析功能，包括图形数据、表格数据、项目巡检统计等多维度的数据分析和报表生成
 * 支持复杂的多线程数据处理、远程服务调用、数据计算和报表导出
 * 
 * <AUTHOR>
 * @date 2023/6/14 10:12
 */
@Service
@Slf4j
public class IntegrativeStatisticServiceImpl implements IntegrativeStatisticService {
    @Autowired
    private IntegrativeStatisticMapper integrativeStatisticMapper;
    @Autowired
    private SystemServiceClient systemServiceClient;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private AlarmServiceClient alarmServiceClient;

    @Autowired
    private PlantServiceClient plantServiceClient;

    /**
     * 综合统计图形数据查询
     *
     * 查询综合统计的图形数据，用于生成各类统计图表，包括发电量趋势、收益分析、效率对比等
     * 支持按项目、时间范围等多维度筛选
     *
     * @param userInfo 用户信息，包含用户权限、项目列表等
     * @param query 综合统计查询条件，包含时间范围、项目筛选、电价等参数
     * @return 综合统计图形数据列表，包含图表所需的数据点和指标
     */
    @Override
    public List<IntegrativeStatisticChartVO> integrativeStatisticChart(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query) {
        if (query.getProjectId() != null && !"".equals(query.getProjectId())) {
            List<String> projectList = systemServiceClient.getProjectIDListByPid(query.getProjectId()).getData();
            userInfo.setProjectList(projectList);
        }
        // 判断传入时间类型是日期类型
        Boolean isMonth = false;
        if (query.getDataStartTime().length() == Constant.MONTH_LENGTH) {
            isMonth = true;
        }
        query.setDataStartTime(DateUtils.getBeginOfMonth(query.getDataStartTime()));
        query.setDataEndTime(DateUtils.getEndOfMonth(query.getDataEndTime()));
        List<IntegrativeStatisticChartVO> chart = integrativeStatisticMapper.getIntegrativeStatisticChart(userInfo, query, isMonth);
        chart.forEach(chartInfo -> {
            BigDecimal electricity = new BigDecimal(chartInfo.getElectricity());
            BigDecimal plantCapacity = new BigDecimal(chartInfo.getPlantCapacity());
            chartInfo.setIncome(electricity.multiply(new BigDecimal(query.getElectricityPrice())).setScale(2, RoundingMode.HALF_UP).toString());
            if (plantCapacity.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal dailyEfficiencyPerHour = electricity.divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP);
                chartInfo.setDailyEfficiencyPerHour(dailyEfficiencyPerHour.toString());
            } else {
                chartInfo.setDailyEfficiencyPerHour(BigDecimal.ZERO.toString());
            }
        });
        return chart;
    }

    /**
     * 综合统计表格数据查询
     *
     * 查询综合统计的表格数据，用于生成详细的统计报表，包括电站基础信息、发电量、收益、效率等
     * 支持复杂的数据计算和汇总
     *
     * @param userInfo 用户信息，包含用户权限、项目列表等
     * @param query 综合统计查询条件
     * @return 综合统计表格数据对象，包含详细的统计指标和汇总数据
     * @throws Exception 当数据处理异常时抛出
     */
    @Override
    public IntegrativeStatisticSheetVO integrativeStatisticSheet(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query) throws Exception {
        if (query.getProjectId() != null && !"".equals(query.getProjectId())) {
            List<String> projectList = systemServiceClient.getProjectIDListByPid(query.getProjectId()).getData();
            userInfo.setProjectList(projectList);
        }
        // 查询电站基础信息统计数据
        IntegrativeStatisticSheetVO integrativeStatisticSheet = integrativeStatisticMapper.getIntegrativeStatisticSheet(userInfo, query);
        String periodElectricity = integrativeStatisticMapper.getPeriodElectricity(userInfo, query);
        integrativeStatisticSheet.setPeriodElectricity(periodElectricity);
        List<HashMap<String, String>> deviceNumInfo = integrativeStatisticMapper.getDeviceNumInfo(userInfo, query);
        // 计算电站基础信息统计数据
        String electricity = integrativeStatisticSheet.getElectricity();
        String electricityPrice = query.getElectricityPrice();
        integrativeStatisticSheet.setIncome(BusinessCalculateUtil.getIncome(electricity, electricityPrice));
        integrativeStatisticSheet.setElectricity(BusinessCalculateUtil.getRealElectricity(electricity));
        integrativeStatisticSheet.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(integrativeStatisticSheet.getPlantCapacity()));
        integrativeStatisticSheet.setTreeNum(BusinessCalculateUtil.getTreeNum(electricity));
        integrativeStatisticSheet.setEfficiencyPerHours(String.format("%.2f", Double.parseDouble(periodElectricity) / 100 / Double.parseDouble(integrativeStatisticSheet.getPlantCapacity())));
        integrativeStatisticSheet.setReduceCo2(BusinessCalculateUtil.getReduceCo2(electricity));
        integrativeStatisticSheet.setReduceCoal(BusinessCalculateUtil.getReduceCoal(electricity));
        // 通过反射动态赋值
        for (HashMap<String, String> map : deviceNumInfo) {
            // 利用枚举寻找字段值
            String status = DeviceStatusField.getNameByCode(String.valueOf(map.get("plantStatus")));
            String statusNum = String.valueOf(map.get("statusNum"));
            ReflectUtil.setFieldValueByName(integrativeStatisticSheet, status, statusNum);
        }
        return integrativeStatisticSheet;
    }

    /**
     * 项目巡检统计
     *
     * 统计指定时间范围内各项目的巡检情况，包括电站数量、发电量、告警情况、工单状态等
     * 支持按项目专项筛选，生成项目巡检报表
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param projectSpecial 项目专项ID，可为空（查询全部项目）
     * @return 项目巡检统计信息列表，包含各项目的详细统计数据
     */
    @Override
    public List<ProjectInspectionInfoVO> projectInspectionStatistics(String startDate, String endDate, String projectSpecial) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String tableName = TableNameConstant.PLANT_DAY;
        if (DateUtils.checkDate(startDate) && DateUtils.checkDate(endDate)) {
            if (startDate.length() != Constant.DAY_LENGTH && endDate.length() != Constant.DAY_LENGTH) {
                tableName = TableNameConstant.PLANT_MONTH;
            }
            List<ProjectInspectionInfoVO> list = integrativeStatisticMapper.projectInspectionStatistics(startDate, endDate, projectSpecial, tableName,userInfo);

            // 解决多线程feign 无法传递请求头问题
            RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
            CountDownLatch countDownLatch = new CountDownLatch(2);

            ThreadFactoryUtils threadFactory = new ThreadFactoryUtils("projectStatistics");
            ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                    5,
                    10,
                    30L,
                    TimeUnit.SECONDS,
                    new SynchronousQueue<>(),
                    threadFactory);
            // 工单状态统计
            executorService.submit(() -> {
                try {
                    Result result = alarmServiceClient.statisticWithProjectId(null, startDate, endDate);
                    if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
                        Object data = result.getData();
                        HashMap<String, List<WorkStatusStatisticVO>> map = JSON.parseObject(JSON.toJSONString(data), new TypeReference<HashMap<String, List<WorkStatusStatisticVO>>>() {
                        });
                        if (CollUtil.isNotEmpty(map)) {
                            processStatuses(map, "normal", list);
                            processStatuses(map, "suspended", list);
                        }
                    } else {
                        throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                                ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage());
                    }
                } finally {
                    countDownLatch.countDown();

                }
            });
            // 查询负责人
            executorService.submit(() -> {
                try {
                    Result<List<DutyPersonVO>> result = systemServiceClient.list(Collections.emptyList());
                    if (ResultEnum.SUCCESS.getCode().equals(result.getStatus())) {
                        List<DutyPersonVO> dutyPersonList = result.getData();
                        if (CollUtil.isNotEmpty(dutyPersonList)) {
                            // 使用 Map 存储数据，键为 city 和 projectSpecial 的组合
                            Map<String, DutyPersonVO> groupedByCityAndProject = dutyPersonList.stream()
                                    .collect(Collectors.toMap(
                                            vo -> vo.getCity() + RedisKey.REDIS_SPLIT + vo.getProjectSpecial(),
                                            vo -> vo,
                                            (vo1, vo2) -> vo1
                                    ));
                            list.forEach(item -> {
                                DutyPersonVO dutyPersonVO = groupedByCityAndProject.get(item.getCity() + RedisKey.REDIS_SPLIT + item.getProjectId());
                                if (dutyPersonVO != null) {
                                    item.setDutyPerson(dutyPersonVO.getDutyPerson());
                                    item.setMaintenanceList(dutyPersonVO.getMaintenanceList());
                                }
                            });
                        }
                    } else {
                        throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                                ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage());
                    }
                } finally {
                    countDownLatch.countDown();
                }
            });

            try {
                countDownLatch.await();
                // 计算日等效小时
                list.forEach(projectInspectionInfo -> {
                    projectInspectionInfo.setDailyEfficiencyPerHour(BusinessCalculateUtil.getRealEfficiencyPerHours(projectInspectionInfo.getElectricity(), projectInspectionInfo.getPlantCapacity()));
                    projectInspectionInfo.setProjectId(globalParamUtil.getProjectNameByProjectId(projectInspectionInfo.getProjectId()));
                });
                // 求出list合计记录
                ProjectInspectionInfoVO total = new ProjectInspectionInfoVO();
                total.setPlantType("合计:");
                int sumPlantNum = list.stream().mapToInt(p -> Integer.parseInt(p.getPlantNum())).sum();
                double sumPlantCapacity = list.stream().mapToDouble(p -> Double.parseDouble(p.getPlantCapacity())).sum();
                double sumElectricity = list.stream().mapToDouble(p -> Double.parseDouble(p.getElectricity())).sum();
                double avgDailyEfficiencyPerHour = list.stream().mapToDouble(p -> Double.parseDouble(p.getDailyEfficiencyPerHour())).average()
                        .orElse(0.0);
                BigDecimal avgHours = new BigDecimal(avgDailyEfficiencyPerHour).setScale(2, BigDecimal.ROUND_CEILING);
                int sumAlarmPlantNum = list.stream().mapToInt(p -> Integer.parseInt(p.getAlarmPlantNum())).sum();
                total.setPlantNum(Integer.toString(sumPlantNum));
                total.setPlantCapacity(Double.toString(sumPlantCapacity));
                total.setElectricity(Double.toString(sumElectricity));
                total.setDailyEfficiencyPerHour(avgHours.toString());
                total.setAlarmPlantNum(Integer.toString(sumAlarmPlantNum));

                AtomicInteger totalNormalCount = new AtomicInteger(list.stream().mapToInt(ProjectInspectionInfoVO::getNormalCount).sum());
                AtomicInteger totalSuspendedCount = new AtomicInteger(list.stream().mapToInt(ProjectInspectionInfoVO::getSuspendedCount).sum());

                total.setNormalCount(totalNormalCount.get());
                total.setSuspendedCount(totalSuspendedCount.get());
                list.add(total);
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException(ResultEnum.OPERATION_FAILED);
            } finally {
                executorService.shutdown();
            }
            return list;
        }
        throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED);

    }

    /**
     * 获取电站月度发电量数据
     *
     * 查询指定电站月度每日的发电量数据，用于生成月度发电量报表
     *
     * @param userInfo 用户信息，用于数据权限控制
     * @param query 电站统计查询条件，包含时间范围、电站筛选等参数
     * @return 电站月度发电量数据列表，包含每日发电量和电站信息
     */
    @Override
    public List<PlantMonthStatementVO> getMonthElectricity(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) {
        return integrativeStatisticMapper.getMonthElectricity(userInfo, query);
    }

    /**
     * 获取电站月度告警数据
     *
     * 查询指定电站月度每日的告警状态数据，用于生成月度告警报表
     *
     * @param userInfo 用户信息，用于数据权限控制
     * @param query 电站统计查询条件
     * @return 电站月度告警数据列表，包含每日告警状态和电站信息
     */
    @Override
    public List<PlantMonthStatementVO> getMonthAlarm(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) {
        return integrativeStatisticMapper.getMonthAlarm(userInfo, query);
    }

    /**
     * 处理工单状态
     *
     * 根据工单状态统计结果，为项目巡检信息列表设置对应的工单计数
     *
     * @param map 工单状态统计结果映射
     * @param status 工单状态类型
     * @param list 项目巡检信息列表
     */
    private static void processStatuses(HashMap<String, List<WorkStatusStatisticVO>> map, String status, List<ProjectInspectionInfoVO> list) {
        List<WorkStatusStatisticVO> statusList = map.get(status);
        if (CollUtil.isNotEmpty(statusList)) {
            Map<String, WorkStatusStatisticVO> statusMap = statusList.stream()
                    .collect(Collectors.toMap(
                            vo -> vo.getCity() + RedisKey.REDIS_SPLIT + vo.getProjectId(),
                            vo -> vo
                    ));

            list.forEach(item -> {
                WorkStatusStatisticVO workStatusStatisticVO = statusMap.get(item.getCity() + RedisKey.REDIS_SPLIT + item.getProjectId());
                if (workStatusStatisticVO != null) {
                    setCount(item, workStatusStatisticVO, status);
                }
            });
        }
    }

    /**
     * 设置工单状态计数
     *
     * 根据工单状态统计结果，为项目巡检信息对象设置对应的工单计数
     *
     * @param item 项目巡检信息对象
     * @param workStatusStatisticVO 工单状态统计结果对象
     * @param status 工单状态类型
     */
    private static void setCount(ProjectInspectionInfoVO item, WorkStatusStatisticVO workStatusStatisticVO, String status) {
        switch (status) {
            case "normal":
                item.setNormalCount(workStatusStatisticVO.getCount());
                break;
            case "suspended":
                item.setSuspendedCount(workStatusStatisticVO.getCount());
                break;
            default:
                // 处理未知状态
                break;
        }
    }

    /**
     * 月度数据统计测试接口
     *
     * 测试接口，用于月度数据的收集和统计分析，包含发电量、装机容量、效率计算等
     *
     * @param start 开始日期
     * @param end 结束日期
     * @return 月度统计数据列表
     */
    @Override
    public List<MonthData> test(String start, String end) {
        BigDecimal all = BigDecimal.ZERO;

        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        BigDecimal count = new BigDecimal(DateUtils.getCountDaysBetween(start, end));

        List<MonthData> result = integrativeStatisticMapper.collect(start, end, userInfo);
        for (MonthData data : result) {

            String realElectricity = Optional.ofNullable(data.getRealElectricity()).orElse("0");
            data.setRealElectricity(BusinessCalculateUtil.getRealElectricity(realElectricity));

            String preElectricity = Optional.ofNullable(data.getPreElectricity()).orElse("0");
            data.setPreElectricity(BusinessCalculateUtil.getRealElectricity(preElectricity));

            String totalCapacity = Optional.ofNullable(data.getTotalCapacity()).orElse("0");
            data.setTotalCapacity(BusinessCalculateUtil.getRealPlantCapacity(totalCapacity));

            // 每月 实际平均每个电站的等效小时
            String efficiency = BusinessCalculateUtil.getEfficiencyPerHours(realElectricity, totalCapacity);

            // 实际 总的等效小时
            data.setEfficiencyHoursPerMonth(efficiency);

            // 平均每个电站
            // String totalPlant = Optional.ofNullable(data.getTotalPlant()).orElse("0");
            // String avgEfficiency = new BigDecimal(efficiency).divide(new BigDecimal(totalPlant)).setScale(2, RoundingMode.HALF_UP).toString();

            String efficiencyHoursPerDay = new BigDecimal(efficiency).divide(count, 2, RoundingMode.HALF_UP).toString();
            // 实际 每日的等效小时
            data.setEfficiencyHoursPerDay(efficiencyHoursPerDay);

            // 预测平均日等效小时
            String preEfficiency = BusinessCalculateUtil.getEfficiencyPerHours(preElectricity, totalCapacity);
            String preEfficiencyHoursPerDay = new BigDecimal(preEfficiency).divide(count, 2, RoundingMode.HALF_UP).toString();
            data.setPreEfficiencyHoursPerDay(preEfficiencyHoursPerDay);


            BigDecimal capacity = new BigDecimal(data.getTotalCapacity());
            all = all.add(capacity);

        }

        MonthData last = new MonthData();
        last.setCity("总结");
        last.setTotalCapacity(all.toString());
        result.add(last);
        return result;
    }

    /**
     * 综合数据导出接口
     *
     * 导出综合统计分析数据，包括低电量电站、达标率分析、发电量排序等多维度数据
     * 直接通过HTTP响应输出Excel文件
     *
     * @param start 开始日期
     * @param end 结束日期
     * @param realTownDayEfficiencyAvgStandard 实际镇日平均效率达标标准值
     * @param prePowerRateStandard 预测发电率达标标准值
     * @param response HTTP响应对象，用于直接输出文件流
     */
    @Override
    public void all(String start, String end, String realTownDayEfficiencyAvgStandard, String prePowerRateStandard, HttpServletResponse response) {
        AllData last = new AllData();
        last.setPlantName("总结");
        BigDecimal totalCapacity = BigDecimal.ZERO;
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();

        // 设置达标率
        if (StrUtil.isBlank(realTownDayEfficiencyAvgStandard)) {
            realTownDayEfficiencyAvgStandard = "0.65";
        }
        if (StrUtil.isBlank(prePowerRateStandard)) {
            prePowerRateStandard = "0.8";
        }
        BigDecimal powerRateStandard = new BigDecimal(prePowerRateStandard);
        BigDecimal townDayEfficiencyAvgStandard = new BigDecimal(realTownDayEfficiencyAvgStandard);

        // 获取数据
        List<AllData> result = integrativeStatisticMapper.all(start, end, userInfo);

        // 获取市-镇 月均 发电量
        List<String> area = result.stream().map(AllData::getArea).distinct().collect(Collectors.toList());
        List<String> town = result.stream().map(AllData::getTown).distinct().collect(Collectors.toList());
        String startStr = DateUtils.format(start, DateUtils.DATE_MONTH_PATTERN);
        String endStr = DateUtils.format(end, DateUtils.DATE_MONTH_PATTERN);
        HourTownQuery hourTownQuery = new HourTownQuery(area, town);
        Result<List<HourTownEntity>> hourTownData = plantServiceClient.list(startStr, endStr, hourTownQuery);
        if (!hourTownData.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD);
        }
        List<HourTownEntity> hourTown = hourTownData.getData(new TypeReference<List<HourTownEntity>>() {
        });

        Map<String, String> townMap = hourTown.stream().collect(Collectors.toMap(
                o -> o.getCity() + RedisKey.REDIS_SPLIT + o.getTown(),
                HourTownEntity::getAvgHourR,
                (o1, o2) -> o1
        ));

        Map<String, BigDecimal> realCityMap = new HashMap<>();
        Map<String, BigDecimal> preCityMap = new HashMap<>();

        for (HourTownEntity data : hourTown) {
            String city = data.getCity();

            BigDecimal real = realCityMap.getOrDefault(city, BigDecimal.ZERO);
            BigDecimal sum1 = real.add(new BigDecimal(data.getAvgHourR()));
            realCityMap.put(city, sum1);

            BigDecimal pre = preCityMap.getOrDefault(city, BigDecimal.ZERO);
            BigDecimal sum2 = pre.add(new BigDecimal(data.getAvgHourP()));
            preCityMap.put(city, sum2);
        }


        List<AllData> lowPower = new ArrayList<>();


        for (AllData data : result) {
            // 建站日期
            String createTime = data.getCreateTime();
            // 发电天数
            BigDecimal count;
            if (DateUtils.isBefore(createTime, start)) {
                count = new BigDecimal(DateUtils.getCountDaysBetween(start, end));
            } else {
                count = new BigDecimal(DateUtils.getCountDaysBetween(createTime, end)).subtract(BigDecimal.ONE);
            }

            if (count.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 电站容量
            String capacity = Optional.ofNullable(data.getPlantCapacity()).orElse("0");
            data.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(capacity));

            // 实际发电量
            String realElectricity = Optional.ofNullable(data.getRealElectricity()).orElse("0");
            data.setRealElectricity(BusinessCalculateUtil.getRealElectricity(realElectricity));
            // 实际等效小时
            data.setRealEfficiency(BusinessCalculateUtil.getEfficiencyPerHours(realElectricity, capacity));
            // 实际日均等效小时
            BigDecimal realEfficiencyNum = new BigDecimal(data.getRealEfficiency());
            data.setRealEfficiencyPerDay(realEfficiencyNum.divide(count, 2, RoundingMode.HALF_UP).toString());


            // 预测发电量
            String preElectricity = Optional.ofNullable(data.getPreElectricity()).orElse("0");
            data.setPreElectricity(BusinessCalculateUtil.getRealElectricity(preElectricity));
            // 预测等效小时
            data.setPreEfficiency(BusinessCalculateUtil.getEfficiencyPerHours(preElectricity, capacity));
            // 预测日均等效小时
            BigDecimal preEfficiencyNum = new BigDecimal(data.getPreEfficiency());
            data.setPreEfficiencyPerDay(preEfficiencyNum.divide(count, 2, RoundingMode.HALF_UP).toString());


            // 预测发电效率
            String d1 = data.getRealEfficiencyPerDay(), d2 = data.getPreEfficiencyPerDay();
            BigDecimal decimal = new BigDecimal(d2);
            BigDecimal prePowerRate;
            if (decimal.compareTo(BigDecimal.ZERO) == 0) {
                prePowerRate = BigDecimal.ZERO;
            } else {
                prePowerRate = new BigDecimal(d1).divide(decimal, 2, RoundingMode.HALF_UP);
            }

            // prePowerRate
            // String prePowerRate = BusinessCalculateUtil.getPercent(d1, d2);
            data.setPrePowerRate(prePowerRate.toString());

            // 镇平均日均等效小时
            BigDecimal townDayAvg = new BigDecimal(townMap.getOrDefault(data.getCity() + RedisKey.REDIS_SPLIT + data.getTown(), "0.00"));
            data.setRealTownDayEfficiencyAvg(townDayAvg.toString());


            // 日均等效小时达标率
            BigDecimal realEfficiencyRate = null;
            BigDecimal member = new BigDecimal(data.getRealEfficiencyPerDay());
            if (townDayAvg.compareTo(BigDecimal.ZERO) == 0) {
                data.setRate("0.00");
            } else {
                realEfficiencyRate = member.divide(townDayAvg, 2, RoundingMode.HALF_UP);
                data.setRate(realEfficiencyRate.toString());
            }

            // BigDecimal realEfficiencyRate = member.divide(townDayAvg, 2, RoundingMode.HALF_UP);
            // data.setRate(realEfficiencyRate.toString());

            // 低电量电站
            if (prePowerRate.compareTo(powerRateStandard) == -1 && isLessThan(townDayEfficiencyAvgStandard, new BigDecimal(data.getRate()))) {
                lowPower.add(data);
            }

            BigDecimal decimal1 = new BigDecimal(data.getPlantCapacity());
            totalCapacity = totalCapacity.add(decimal1);
        }

        result.sort((o1, o2) -> {
            BigDecimal b1 = o1.getRealEfficiencyPerDay() == null ? BigDecimal.ZERO : new BigDecimal(o1.getRealEfficiencyPerDay());
            BigDecimal b2 = o2.getRealEfficiencyPerDay() == null ? BigDecimal.ZERO : new BigDecimal(o2.getRealEfficiencyPerDay());
            return b1.compareTo(b2);
        });

        //#===============================================
        last.setPlantCapacity(totalCapacity.toString());
        result.add(last);
        //#===============================================

        List<MonthData> testData = test(start, end);
        for (MonthData data : testData) {
            String city = data.getCity();
            BigDecimal pre = preCityMap.getOrDefault(city, BigDecimal.ZERO);
            BigDecimal real = realCityMap.getOrDefault(city, BigDecimal.ZERO);
            data.setPreEfficiencyHoursPerDay(pre.toString());
            data.setEfficiencyHoursPerDay(real.toString());
        }

        List<List<?>> dataList = new ArrayList<>();
        dataList.add(testData);

        dataList.add(lowPower);
        dataList.add(result);
        ArrayList<String> names = new ArrayList<>();
        names.add("汇总");
        names.add("低电量输出");
        names.add("电量排序");
        CreateExcelUtils.exportToExcelMulti(dataList, names, response);
        // return result;
    }

    /**
     * 判断是否小于标准值
     *
     * @param standard 标准值
     * @param data     数据
     * @return true if data < standard, false otherwise
     */
    private static boolean isLessThan(BigDecimal standard, BigDecimal data) {
        if (standard.compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }
        return data.compareTo(standard) == -1;
    }
}
