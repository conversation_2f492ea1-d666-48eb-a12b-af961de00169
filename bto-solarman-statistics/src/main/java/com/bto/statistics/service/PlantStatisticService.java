package com.bto.statistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.StatisticsQueryDTO;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import com.bto.commons.pojo.dto.PlantStatisticsQueryDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.response.Result;
import com.bto.statistics.pojo.LowPowerPlantVO;
import com.github.pagehelper.PageInfo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 电站统计服务接口
 * 提供电站相关的全面统计分析功能，包括发电量统计、电站信息管理、能效分析、收益统计、低电量电站监控等
 * 
 * <AUTHOR>
 * @date 2023/4/24 9:29
 */
public interface PlantStatisticService {
    /**
     * 获取近七天发电量数据
     * 
     * 查询当前用户权限范围内所有电站最近7天的每日发电量数据
     * 数据按日期升序排列，用于生成发电量趋势图表
     * 
     * @param userInfo 用户权限信息，包含用户ID、项目列表等权限控制信息
     * @return 近七天发电量数据列表，每条数据包含日期和对应的发电量
     */
    List<ChartElectricityInfoVO> getElectricityBySevenDay(RequireParamsDTO userInfo);

    /**
     * 根据电站ID获取近七天发电量数据
     * 
     * 查询指定电站最近7天的每日发电量数据
     * 数据按日期升序排列，用于电站详情页面的发电量趋势展示
     * 
     * @param plantId 电站唯一标识
     * @return 指定电站近七天发电量数据列表
     */
    List<BatteryDivinerVO> getElectricityBySevenDay(String plantId);

    /**
     * 获取最近一个月每日的发电量数据
     * 
     * 查询当前用户权限范围内所有电站最近指定天数的每日发电量数据
     * 支持灵活的天数设置，用于不同周期的发电量分析
     * 
     * @param userInfo 用户权限信息
     * @param dayNums 查询天数，如30表示最近30天
     * @return 指定天数的发电量数据列表
     */
    List<ChartElectricityInfoVO> getElectricityByNumDay(RequireParamsDTO userInfo, Integer dayNums);
    
    /**
     * 获取近六个月发电量数据
     * 
     * 查询当前用户权限范围内所有电站最近6个月的月度发电量数据
     * 数据按月份升序排列，用于半年周期的发电量趋势分析
     * 
     * @param userInfo 用户权限信息
     * @return 近六个月发电量数据列表
     */
    List<ChartElectricityInfoVO> getElectricityBySixMonth(RequireParamsDTO userInfo);

    /**
     * 获取电站发电量与工作效率数据
     * 
     * 查询当前用户权限范围内的电站综合信息
     *
     * @param userInfo 用户权限信息
     * @return 电站发电量和工作效率综合信息对象
     */
    PlantElectricityVO getPlantElectricityInfo(RequireParamsDTO userInfo);

    /**
     * 获取电站统计数量信息
     * 
     * 查询当前用户权限范围内的电站数量统计
     *
     * @param userInfo 用户权限信息
     * @return 电站统计信息DTO，包含各类电站状态的数量和比率
     * @throws NoSuchFieldException 当电站状态字段不存在时抛出
     * @throws IllegalAccessException 当电站状态字段访问受限时抛出
     */
    NumInfoDTO getPlantNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException;

    /**
     * 能效收益统计-区域/发电统计
     * 
     * 查询指定条件下的能效收益统计数据，支持按区域、电站等多种维度统计
     * 包含发电量、收益、等效小时数、同比环比分析等关键指标
     * 
     * @param query 电量统计查询条件，包含时间范围、区域筛选、电站筛选等参数
     * @param userInfo 用户权限信息
     * @return 分页的能效收益统计信息列表
     */
    PageInfo<ElectricityStaticsInfoVO> getElectricityStatisticsInfo(ElectricityStatisticsQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 查询所有电站当日每小时发电量统计
     * 
     * 查询当前用户权限范围内所有电站当日的每小时发电量数据
     * 用于实时监控和当日发电量趋势分析
     * 
     * @param userInfo 用户权限信息
     * @return 当日每小时发电量数据列表，包含24小时的发电量数据点
     */
    List<ChartElectricityInfoVO> getEveryHourElectricityInfo(RequireParamsDTO userInfo);

    /**
     * 获取电站统计数据信息
     * 
     * 分页查询电站的详细统计信息
     *
     * @param query 电站统计查询条件，包含分页、排序、筛选等参数
     * @param userInfo 用户权限信息
     * @return 分页的电站统计信息列表
     */
    Page<PlantStatisticsInfoVO> getPlantStatisticsInfo(PlantStatisticsQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 获取电站等效利用小时数分布
     * 
     * 统计指定条件下电站的等效利用小时数分布情况，按5个区间进行分布统计
     * 用于评估电站的发电效率和使用情况
     * 
     * @param query 电站统计查询条件
     * @param userInfo 用户权限信息
     * @param projectId 项目ID，可为空
     * @return 等效小时数分布Map，key为区间范围，value为对应区间的电站数量
     */
    Map<String, Integer> getPlantEquivalentUseHour(PlantStatisticsQueryDTO query, RequireParamsDTO userInfo, String projectId);

    /**
     * 查询统计数据（策略模式）
     * 
     * 统一的统计数据查询入口，采用策略模式支持多种统计类型：
     * - 发电统计
     * - 用电统计
     * - 储电统计
     * - 收益统计
     * 
     * @param queryDTO 统计查询参数，包含统计类型、时间范围、设备列表等参数
     * @return 统一封装的统计结果对象
     */
    Result<?> queryStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 导出低电量电站数据
     * 
     * 根据查询条件导出低电量电站数据，直接通过HTTP响应输出Excel文件
     * 低电量电站定义为发电量低于预期标准的电站
     * 
     * @param query 低电量电站查询条件
     * @param userInfo 用户权限信息
     * @param response HTTP响应对象，用于直接输出文件流
     */
    void exportLowPowerPlant(LowPowerQuery query, RequireParamsDTO userInfo, HttpServletResponse response);

    /**
     * 获取低电量电站列表
     * 
     * 查询低电量电站列表，支持分页和条件筛选
     * 返回的电站数据包含详细的发电信息和状态信息
     * 
     * @param query 低电量电站查询条件
     * @param userInfo 用户权限信息
     * @return 低电量电站列表，包含电站详细信息和发电数据
     */
    List<LowPowerPlantVO> getLowPowerPlant(LowPowerQuery query, RequireParamsDTO userInfo);
}
