package com.bto.statistics.service;

import com.bto.commons.pojo.dto.IntegrativeStatisticQueryDTO;
import com.bto.commons.pojo.dto.PlantStatisticsExportFileDTO;
import com.bto.commons.pojo.vo.*;
import com.bto.statistics.pojo.MonthData;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 综合统计服务接口
 * 提供综合性的数据统计分析功能，包括图形数据、表格数据、项目巡检统计等多维度的数据分析和报表生成
 * 
 * <AUTHOR>
 * @date 2023/6/14 10:11
 */
public interface IntegrativeStatisticService {

    /**
     * 综合统计图形数据查询
     * 
     * 查询综合统计的图形数据，用于生成各类统计图表，包括发电量趋势、收益分析、效率对比等
     * 支持按项目、时间范围等多维度筛选
     * 
     * @param userInfo 用户信息，包含用户权限、项目列表等
     * @param query 综合统计查询条件，包含时间范围、项目筛选、电价等参数
     * @return 综合统计图形数据列表，包含图表所需的数据点和指标
     */
    List<IntegrativeStatisticChartVO> integrativeStatisticChart(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query);

    /**
     * 综合统计表格数据查询
     * 
     * 查询综合统计的表格数据，用于生成详细的统计报表，包括电站基础信息、发电量、收益、效率等
     * 支持复杂的数据计算和汇总
     * 
     * @param userInfo 用户信息，包含用户权限、项目列表等
     * @param query 综合统计查询条件
     * @return 综合统计表格数据对象，包含详细的统计指标和汇总数据
     * @throws Exception 当数据处理异常时抛出
     */
    IntegrativeStatisticSheetVO integrativeStatisticSheet(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query) throws Exception;

    /**
     * 项目巡检统计
     * 
     * 统计指定时间范围内各项目的巡检情况，包括电站数量、发电量、告警情况、工单状态等
     * 支持按项目专项筛选，生成项目巡检报表
     * 
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param projectSpecial 项目专项ID，可为空（查询全部项目）
     * @return 项目巡检统计信息列表，包含各项目的详细统计数据
     */
    List<ProjectInspectionInfoVO> projectInspectionStatistics(String startDate,String endDate,String projectSpecial);

    /**
     * 获取电站月度发电量数据
     * 
     * 查询指定电站月度每日的发电量数据，用于生成月度发电量报表
     * 
     * @param userInfo 用户信息，用于数据权限控制
     * @param query 电站统计查询条件，包含时间范围、电站筛选等参数
     * @return 电站月度发电量数据列表，包含每日发电量和电站信息
     */
    List<PlantMonthStatementVO> getMonthElectricity(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query);

    /**
     * 获取电站月度告警数据
     * 
     * 查询指定电站月度每日的告警状态数据，用于生成月度告警报表
     * 
     * @param userInfo 用户信息，用于数据权限控制
     * @param query 电站统计查询条件
     * @return 电站月度告警数据列表，包含每日告警状态和电站信息
     */
    List<PlantMonthStatementVO> getMonthAlarm(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query);

    /**
     * 月度数据统计测试接口
     * 
     * 测试接口，用于月度数据的收集和统计分析，包含发电量、装机容量、效率计算等
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @return 月度统计数据列表
     */
    List<MonthData> test(String start, String end);

    /**
     * 综合数据导出接口
     * 
     * 导出综合统计分析数据，包括低电量电站、达标率分析、发电量排序等多维度数据
     * 直接通过HTTP响应输出Excel文件
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @param realTownDayEfficiencyAvgStandard 实际镇日平均效率达标标准值
     * @param prePowerRateStandard 预测发电率达标标准值
     * @param response HTTP响应对象，用于直接输出文件流
     */
    void all(String start, String end, String realTownDayEfficiencyAvgStandard, String prePowerRateStandard, HttpServletResponse response);
}
