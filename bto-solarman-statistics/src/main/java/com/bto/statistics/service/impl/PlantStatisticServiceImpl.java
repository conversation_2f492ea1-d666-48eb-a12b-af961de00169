package com.bto.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.constant.Constant;
import com.bto.commons.constant.PlantStatusEnum;
import com.bto.commons.constant.UserEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import com.bto.commons.pojo.dto.NumInfoDTO;
import com.bto.commons.pojo.dto.PlantStatisticsQueryDTO;
import com.bto.commons.pojo.dto.StatisticsQueryDTO;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.*;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.statistics.dao.PlantAllStatisticsMapper;
import com.bto.statistics.dao.PlantStatisticsMapper;
import com.bto.statistics.pojo.LowPowerPlantVO;
import com.bto.statistics.service.PlantStatisticService;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bto.commons.constant.Constant.*;

/**
 * 电站统计服务实现类
 * 实现电站相关的全面统计分析功能，包括发电量统计、电站信息管理、能效分析、收益统计、低电量电站监控等
 * 采用策略模式处理不同类型的统计请求，支持复杂的数据计算、同比环比分析、分页查询等功能
 * 
 * <AUTHOR>
 * @date 2023/4/24 9:29
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlantStatisticServiceImpl implements PlantStatisticService {

    private final PlantStatisticsMapper plantStatisticsMapper;
    private final PlantAllStatisticsMapper statisticsMapper;

    /**
     * 使用ThreadLocal保存当前查询的DTO对象
     * ThreadLocal可以为每个线程提供独立的变量副本，避免参数在方法之间传递
     * 这在处理并发请求时特别有用，确保线程之间数据不会相互干扰
     */
    private static final ThreadLocal<StatisticsQueryDTO> CURRENT_DTO = new ThreadLocal<>();

    // 统计类型枚举
    private static final int GENERATION_TYPE = 1; // 发电统计
    private static final int LOAD_TYPE = 2;       // 用电统计
    private static final int BATTERY_TYPE = 3;    // 储电统计
    private static final int REVENUE_TYPE = 4;    // 收益统计

    // 日期类型常量
    private static final String DATE_TYPE_DAY = "day";

    /**
     * 策略模式的核心：定义一个映射，将不同的统计类型关联到对应的处理函数
     * 这里使用了Java 8的Function接口，代表一个接受StatisticsQueryDTO输入并返回Object的函数
     * 这种设计比传统的if-else或switch语句更具扩展性和灵活性
     * 解释：
     * - 外层Map是一个容器，key是统计类型，value是对应的处理函数
     * - Function<StatisticsQueryDTO, Object>是一个函数式接口，表示接受一个DTO参数并返回一个结果对象
     * - this::handleXxxStatistics是方法引用语法，指向当前类中的具体处理方法
     */
    private final Map<Integer, Function<StatisticsQueryDTO, Object>> statisticsHandlers = new HashMap<Integer, Function<StatisticsQueryDTO, Object>>() {{
        put(GENERATION_TYPE, PlantStatisticServiceImpl.this::handleGenerationStatistics);  // 发电统计
        put(LOAD_TYPE, PlantStatisticServiceImpl.this::handleLoadStatistics);              // 用电统计
        put(BATTERY_TYPE, PlantStatisticServiceImpl.this::handleBatteryStatistics);        // 储电统计
        put(REVENUE_TYPE, PlantStatisticServiceImpl.this::handleRevenueStatistics);        // 收益统计
    }};

    @Autowired
    private SystemServiceClient systemServiceClient;

    @Autowired
    private GlobalParamUtil globalParamUtil;

    /**
     * 获取低电量电站列表
     *
     * 查询低电量电站列表，支持分页和条件筛选
     * 返回的电站数据包含详细的发电信息和状态信息
     *
     * @param query 低电量电站查询条件
     * @param userInfo 用户权限信息
     * @return 低电量电站列表，包含电站详细信息和发电数据
     */
    @Override
    public List<LowPowerPlantVO> getLowPowerPlant(LowPowerQuery query, RequireParamsDTO userInfo) {
        // 只查询户租的
        // String id = "4";
        // List<String> scope = systemServiceClient.getProjectIDListByPid(id).getData();

        List<LowPowerPlantVO> res = plantStatisticsMapper.exportLowPowerPlant(query.getDate(), userInfo, null);
        List<ProjectBaseVO> allProjectList = globalParamUtil.getAllProjectList();
        for (LowPowerPlantVO item : res) {
            item.setStatus(PlantStatusEnum.getNameByCode(item.getStatus()));
            item.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(item.getPlantCapacity()));
            for (ProjectBaseVO projectBaseVO : allProjectList) {
                if (item.getProjectSpecial().equals(projectBaseVO.getId())) {
                    item.setProjectName(projectBaseVO.getProjectName());
                }
            }
        }

        return res;
    }

    /**
     * 导出低电量电站数据
     *
     * 根据查询条件导出低电量电站数据，直接通过HTTP响应输出Excel文件
     * 低电量电站定义为发电量低于预期标准的电站
     *
     * @param query 低电量电站查询条件
     * @param userInfo 用户权限信息
     * @param response HTTP响应对象，用于直接输出文件流
     */
    @Override
    public void exportLowPowerPlant(LowPowerQuery query, RequireParamsDTO userInfo, HttpServletResponse response) {
        query.setPageSize(-1);
        query.setCurrentPage(-1);
        List<LowPowerPlantVO> res = this.getLowPowerPlant(query, userInfo);
        CreateExcelUtils.exportToExcel(res, LowPowerPlantVO.class, "低电量电站统计" + query.getDate(), response);
    }

    /**
     * 获取近七天发电量数据
     *
     * 查询当前用户权限范围内所有电站最近7天的每日发电量数据
     * 数据按日期升序排列，用于生成发电量趋势图表
     *
     * @param userInfo 用户权限信息，包含用户ID、项目列表等权限控制信息
     * @return 近七天发电量数据列表，每条数据包含日期和对应的发电量
     */
    @Override
    public List<ChartElectricityInfoVO> getElectricityBySevenDay(RequireParamsDTO userInfo) {
        List<ChartElectricityInfoVO> electricityBySevenDay = plantStatisticsMapper.getElectricityByNumDay(userInfo, 7);
        electricityBySevenDay.sort(Comparator.comparing(ChartElectricityInfoVO::getCollectDate));
        return electricityBySevenDay;
    }

    /**
     * 根据电站ID获取近七天发电量数据
     *
     * 查询指定电站最近7天的每日发电量数据
     * 数据按日期升序排列，用于电站详情页面的发电量趋势展示
     *
     * @param plantId 电站唯一标识
     * @return 指定电站近七天发电量数据列表
     */
    @Override
    public List<BatteryDivinerVO> getElectricityBySevenDay(String plantId) {
        List<BatteryDivinerVO> electricityBySevenDay = plantStatisticsMapper.getElectricityBySevenDay(plantId);
        electricityBySevenDay.sort(Comparator.comparing(BatteryDivinerVO::getCollectDate));
        return electricityBySevenDay;
    }

    /**
     * 获取最近一个月每日的发电量数据
     *
     * 查询当前用户权限范围内所有电站最近指定天数的每日发电量数据
     * 支持灵活的天数设置，用于不同周期的发电量分析
     *
     * @param userInfo 用户权限信息
     * @param dayNums 查询天数，如30表示最近30天
     * @return 指定天数的发电量数据列表
     */
    @Override
    public List<ChartElectricityInfoVO> getElectricityByNumDay(RequireParamsDTO userInfo, Integer dayNums) {
        List<ChartElectricityInfoVO> electricityByNumDay = plantStatisticsMapper.getElectricityByNumDay(userInfo, dayNums);
        electricityByNumDay.sort(Comparator.comparing(ChartElectricityInfoVO::getCollectDate));
        return electricityByNumDay;
    }

    /**
     * 获取近六个月发电量数据
     *
     * 查询当前用户权限范围内所有电站最近6个月的月度发电量数据
     * 数据按月份升序排列，用于半年周期的发电量趋势分析
     *
     * @param userInfo 用户权限信息
     * @return 近六个月发电量数据列表
     */
    @Override
    public List<ChartElectricityInfoVO> getElectricityBySixMonth(RequireParamsDTO userInfo) {
        List<ChartElectricityInfoVO> electricityBySixMonth = plantStatisticsMapper.getElectricityBySixMonth(userInfo);
        electricityBySixMonth.sort(Comparator.comparing(ChartElectricityInfoVO::getCollectDate));
        return electricityBySixMonth;
    }

    /**
     * 获取电站发电量与工作效率数据
     *
     * 查询当前用户权限范围内的电站综合信息
     *
     * @param userInfo 用户权限信息
     * @return 电站发电量和工作效率综合信息对象
     */
    @Override
    public PlantElectricityVO getPlantElectricityInfo(RequireParamsDTO userInfo) {
        PlantElectricityVO plantElectricityInfo = plantStatisticsMapper.getPlantElectricityInfo(userInfo);
        PlantElectricityVO workEfficiencyInfo = plantStatisticsMapper.getWorkEfficiencyInfo(userInfo);
        String plantMass = null;
        if (UserEnum.USER_OF_ENTERPRISE.getCode().equals(userInfo.getUserType()) && !userInfo.getProjectList().isEmpty()) {
            // 获取电站故障率
            plantMass = plantStatisticsMapper.getPlantMassByProjectId(userInfo, DateUtil.today());
        } else {
            Integer errorNumber = plantStatisticsMapper.getPlantMassByPlantList(userInfo);
            if (Objects.isNull(errorNumber)) {
                plantMass = "0";
            } else {
                double v = (double) errorNumber / userInfo.getPlantList().size();
                plantMass = String.format("%.4f", v);
            }
        }

        if (workEfficiencyInfo == null) {
            plantElectricityInfo = new PlantElectricityVO();
            workEfficiencyInfo = new PlantElectricityVO();
        }
        plantElectricityInfo.setAverageEfficiency(workEfficiencyInfo.getAverageEfficiency());
        plantElectricityInfo.setPlantName(workEfficiencyInfo.getPlantName());
        plantElectricityInfo.setMaxEfficiency(workEfficiencyInfo.getMaxEfficiency());
        String realEfficiencyPerHours = BusinessCalculateUtil.getRealEfficiencyPerHours(plantElectricityInfo.getTodayElectricity(), plantElectricityInfo.getPlantCapacity());
        plantElectricityInfo.setDailyEfficiencyPerHour(realEfficiencyPerHours);
        plantElectricityInfo.setFailureRate(plantMass);


        String uid = userInfo.getUserUid();
        Result<UserInfoVO> result = systemServiceClient.getUserInfoByUserUid(uid);
        UserInfoVO user = null;
        if (ResultEnum.SUCCESS.getCode().equals(result.getStatus())) {
            user = result.getData();
        }
        if (user != null) {
            Date createTime = user.getCreateTime();
            if (createTime != null) {
                plantElectricityInfo.setUserCreateTime(createTime);

                try {
                    long daysBetween = DateUtil.between(createTime, new Date(), DateUnit.DAY);
                    plantElectricityInfo.setUserAccumulatedDays(Math.toIntExact(daysBetween));
                } catch (Exception e) {
                    throw new BusinessException("日期格式化错误: " + createTime);
                }
            }
        }


        return plantElectricityInfo;
    }

    /**
     * 获取电站统计数量信息
     *
     * 查询当前用户权限范围内的电站数量统计
     *
     * @param userInfo 用户权限信息
     * @return 电站统计信息DTO，包含各类电站状态的数量和比率
     * @throws NoSuchFieldException 当电站状态字段不存在时抛出
     * @throws IllegalAccessException 当电站状态字段访问受限时抛出
     */
    @Override
    public NumInfoDTO getPlantNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException {
        NumInfoDTO numInfo = new NumInfoDTO();
        List<NumInfoVO> numInfoList = plantStatisticsMapper.getPlantNumInfo(userInfo);
        for (NumInfoVO infoDTO : numInfoList) {
            String fieldName = PlantStatusEnum.getFieldNameByCode(infoDTO.getStatus());
            Field field = numInfo.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(numInfo, infoDTO.getStatusNum());
        }
        String totalNum = BusinessCalculateUtil.getTotalNum(numInfo);
        String onlineNum = BusinessCalculateUtil.getPlantOnlineNum(numInfo);
        String normalNum = BusinessCalculateUtil.getRealNormalNum(numInfo);
        numInfo.setOnlineRate(onlineNum, totalNum);
        numInfo.setNormalRate(normalNum, totalNum);
        numInfo.setTotalNum(totalNum);
        numInfo.setOnlineNum(onlineNum);
        numInfo.setNormalNum(normalNum);
        return numInfo;
    }

    /**
     * 能效收益统计-区域/发电统计
     *
     * 查询指定条件下的能效收益统计数据，支持按区域、电站等多种维度统计
     * 包含发电量、收益、等效小时数、同比环比分析等关键指标
     *
     * @param query 电量统计查询条件，包含时间范围、区域筛选、电站筛选等参数
     * @param userInfo 用户权限信息
     * @return 分页的能效收益统计信息列表
     */
    @Override
    public PageInfo<ElectricityStaticsInfoVO> getElectricityStatisticsInfo(ElectricityStatisticsQueryDTO query, RequireParamsDTO userInfo) {
        IPage<PlantStatisticsInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        PlantStatisticsQueryDTO queryInfo = new PlantStatisticsQueryDTO();
        queryInfo.setPlantName(query.getAddress());
        Page<PlantStatisticsInfoVO> plantInfo = plantStatisticsMapper.selectPlantUidList(queryInfo, page, userInfo, null);
        List<String> plantUidArray = plantInfo.getRecords().stream().map(PlantStatisticsInfoVO::getPlantUid).collect(Collectors.toList());
        List<ElectricityStaticsInfoVO> result;
        if (CollUtil.isEmpty(plantUidArray)) {
            return new PageInfo<>();
        }
        if (query.getDate().length() == Constant.DAY_LENGTH) {
            String tableName = TableUtils.getTableName(Constant.TABLE_PREFIX, query.getDate());
            result = plantStatisticsMapper.getElectricityStatisticsInfoByDay(query, tableName, plantUidArray);
            handleResult(result, Constant.DAY_LENGTH);
        } else if (query.getDate().length() == Constant.MONTH_LENGTH) {
            result = plantStatisticsMapper.getElectricityStatisticsInfoByMonth(query, plantUidArray);
            // 计算同比环比
            query.setDate(DateUtils.getMonthOffset(query.getDate(), -1));
            // 查询上个月数据
            List<ElectricityStaticsInfoVO> lastMonthResult = plantStatisticsMapper.getElectricityStatisticsInfoByMonth(query, plantUidArray);
            if (CollUtil.isNotEmpty(lastMonthResult)) {
                // 调用计算同比方法
                result = CalculateYoyRatio(lastMonthResult, result, DAY_START_INDEX, DAY_END_INDEX);
                // 调用计算环比方法
                result = CalculateMomRatio(result, lastMonthResult, DAY_START_INDEX, DAY_END_INDEX);
            }
            handleResult(result, Constant.MONTH_LENGTH);
        } else if (query.getDate().length() == Constant.YEAR_LENGTH) {
            result = plantStatisticsMapper.getElectricityStatisticsInfoByYear(query, plantUidArray);
            // 计算同比环比
            query.setDate(DateUtils.getYearOffset(query.getDate(), -1));
            List<ElectricityStaticsInfoVO> lastYearResult = plantStatisticsMapper.getElectricityStatisticsInfoByYear(query, plantUidArray);
            if (CollUtil.isNotEmpty(lastYearResult)) {
                // 调用计算同比方法
                result = CalculateYoyRatio(lastYearResult, result, MONTH_START_INDEX, MONTH_END_INDEX);
                // 调用计算环比方法
                result = CalculateMomRatio(result, lastYearResult, MONTH_START_INDEX, MONTH_END_INDEX);
            }
            handleResult(result, Constant.YEAR_LENGTH);
        } else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED.getCode(), ResultEnum.DATETIME_FORMAT_FAILED.getMessage());
        }
        PageInfo<ElectricityStaticsInfoVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 处理能效收益统计数据（）
     *
     * @param result
     */
    private void handleResult(List<ElectricityStaticsInfoVO> result, Integer dateLength) {
        result.forEach(electricityStaticsInfo -> {
            List<ElectricityInfoVO> electricityList = electricityStaticsInfo.getElectricityList();
            Iterator<ElectricityInfoVO> iterator = electricityList.iterator();

            BigDecimal totalIncome = BigDecimal.ZERO;
            BigDecimal totalElectricity = BigDecimal.ZERO;
            BigDecimal totalEfficiencyPerHours = BigDecimal.ZERO;

            BigDecimal count = BigDecimal.ZERO;

            while (iterator.hasNext()) {
                count = count.add(BigDecimal.ONE);
                ElectricityInfoVO electricityInfo = iterator.next();

                // 判断是否是最后一个元素
                boolean isLast = !iterator.hasNext();

                // 处理数据
                electricityInfo.setEfficiencyPerHours(BusinessCalculateUtil.getEfficiencyPerHours(electricityInfo.getElectricity(), electricityInfo.getPlantCapacity()));
                electricityInfo.setIncome(BusinessCalculateUtil.getIncome(electricityInfo.getElectricity(), electricityInfo.getPlantPrice()));
                electricityInfo.setElectricity(BusinessCalculateUtil.getRealElectricity(electricityInfo.getElectricity()));
                electricityInfo.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(electricityInfo.getPlantCapacity()));

                // 总收入
                totalIncome = totalIncome.add(new BigDecimal(electricityInfo.getIncome()));
                // 总发电量
                totalElectricity = totalElectricity.add(new BigDecimal(electricityInfo.getElectricity()));
                // 总等效小时
                totalEfficiencyPerHours = totalEfficiencyPerHours.add(new BigDecimal(electricityInfo.getEfficiencyPerHours()));

                // 如果是最后一个元素，统计与平均值
                if (isLast && !dateLength.equals(DAY_LENGTH)) {
                    electricityStaticsInfo.setTotalIncome(totalIncome.toString());
                    electricityStaticsInfo.setTotalElectricity(totalElectricity.toString());
                    electricityStaticsInfo.setTotalEfficiencyPerHours(totalEfficiencyPerHours.toString());

                    electricityStaticsInfo.setAvgIncome(totalIncome.divide(count, 2, RoundingMode.HALF_UP).toString());
                    electricityStaticsInfo.setAvgElectricity(totalElectricity.divide(count, 2, RoundingMode.HALF_UP).toString());
                    electricityStaticsInfo.setAvgEfficiencyPerHours(totalEfficiencyPerHours.divide(count, 2, RoundingMode.HALF_UP).toString());
                }
            }
        });
    }


    /**
     * 同比计算方法
     *
     * @param lastMonthResult
     * @param result
     */
    private List<ElectricityStaticsInfoVO> CalculateYoyRatio(List<ElectricityStaticsInfoVO> lastMonthResult,
                                                             List<ElectricityStaticsInfoVO> result,
                                                             Integer startIndex,
                                                             Integer endIndex) {
        // 判断本月数据记录数是否大于上月数据记录数
        for (int i = 0, j = 0; i < lastMonthResult.size(); i++) {
            String lastPlantName = lastMonthResult.get(i).getPlantName();
            boolean flag = true;
            while (!result.get(j).getPlantName().equals(lastPlantName) && flag) {
                if (j < result.size() - 1) {
                    j++;
                } else {
                    flag = false;
                }
            }
            // 当前电站上月份数据
            List<ElectricityInfoVO> lastDataList = lastMonthResult.get(i).getElectricityList();
            // 当前电站本月份数据
            List<ElectricityInfoVO> curDataList = result.get(j).getElectricityList();
            // 计算同比并赋值给当前电站的月数据
            for (int k = 0, h = 0; k < lastDataList.size(); k++) {
                String lastDataTime = lastDataList.get(k).getDataTime().substring(startIndex, endIndex);
                boolean dateFlag = true;
                while (!lastDataTime.equals(curDataList.get(h).getDataTime().substring(startIndex, endIndex)) && dateFlag) {
                    if (h < curDataList.size() - 1) {
                        h++;
                    } else {
                        dateFlag = false;
                    }
                }
                BigDecimal b1 = new BigDecimal(curDataList.get(h).getElectricity()).subtract(new BigDecimal(lastDataList.get(k).getElectricity())).multiply(new BigDecimal(100));
                BigDecimal b2 = new BigDecimal(lastDataList.get(k).getElectricity());
                if (b2.intValue() > 0) {
                    BigDecimal yoyRatio = b1.divide(b2, 2, RoundingMode.HALF_UP);
                    curDataList.get(h).setYoyRatio(yoyRatio.toString() + "%");
                }
                if (h < curDataList.size() - 1) {
                    h++;
                }
            }
            result.get(j).setElectricityList(curDataList);
            j++;
        }
        return result;
    }

    /**
     * 环比计算方法
     *
     * @param currentResult
     */
    private List<ElectricityStaticsInfoVO> CalculateMomRatio(List<ElectricityStaticsInfoVO> currentResult,
                                                             List<ElectricityStaticsInfoVO> lastResult,
                                                             Integer startIndex,
                                                             Integer endIndex) {
        for (int i = 0, j = 0; i < currentResult.size(); i++) {
            List<ElectricityInfoVO> curElectricityList = currentResult.get(i).getElectricityList();
            String firstDay = currentResult.get(i).getElectricityList().get(0).getDataTime().substring(startIndex, endIndex);
            // 每月的一号需要单独计算（与上个月份最后一天进行对比）
            if ("01".equals(firstDay)) {
                boolean flag = true;
                // 如果第一条数据为1号，则取上个月对应电站的最后一条数据进行比对
                while (!currentResult.get(i).getPlantUid().equals(lastResult.get(j).getPlantUid()) && flag) {
                    if (j < lastResult.size() - 1) {
                        j++;
                    } else {
                        flag = false;
                    }
                }
                List<ElectricityInfoVO> lastElectricityList = lastResult.get(j).getElectricityList();
                if (lastElectricityList.size() > 0) {
                    // 取上月份最后一条数据
                    String endDayElectricity = lastElectricityList.get(lastElectricityList.size() - 1).getElectricity();
                    // 取出本月份第一条数据
                    String firstDayElectricity = curElectricityList.get(0).getElectricity();
                    // 求出环比
                    BigDecimal b1 = new BigDecimal(firstDayElectricity).subtract(new BigDecimal(endDayElectricity)).multiply(new BigDecimal(100));
                    BigDecimal b2 = new BigDecimal(endDayElectricity);
                    if (b2.intValue() > 0) {
                        BigDecimal momRatio = b1.divide(b2, 2, BigDecimal.ROUND_HALF_UP);
                        curElectricityList.get(0).setMomRatio(momRatio.toString() + "%");
                    }
                }
            }
            if (curElectricityList.size() <= 1) {
                currentResult.get(i).setElectricityList(curElectricityList);
                continue;
            }
            // 其余情况皆为当月数据之间的比对
            for (int k = 1; k < curElectricityList.size(); k++) {
                BigDecimal b1 = new BigDecimal(curElectricityList.get(k).getElectricity()).subtract(new BigDecimal(curElectricityList.get(k - 1).getElectricity())).multiply(new BigDecimal(100));
                BigDecimal b2 = new BigDecimal(curElectricityList.get(k - 1).getElectricity());
                if (b2.intValue() > 0) {
                    BigDecimal momRatio = b1.divide(b2, 2, BigDecimal.ROUND_HALF_UP);
                    curElectricityList.get(k).setMomRatio(momRatio.toString() + "%");
                }
            }
        }
        return currentResult;
    }

    /**
     * 查询所有电站当日每小时发电量统计
     *
     * 查询当前用户权限范围内所有电站当日的每小时发电量数据
     * 用于实时监控和当日发电量趋势分析
     *
     * @param userInfo 用户权限信息
     * @return 当日每小时发电量数据列表，包含24小时的发电量数据点
     */
    @Override
    public List<ChartElectricityInfoVO> getEveryHourElectricityInfo(RequireParamsDTO userInfo) {
        // 获取当日日期
        String tableSuffix = DateUtil.today().replace("-", "");
        List<ChartElectricityInfoVO> everyHourElectricityInfo = plantStatisticsMapper.getEveryHourElectricityInfo(tableSuffix, userInfo);
        return everyHourElectricityInfo;
    }

    /**
     * 发电量数据错位相减(留待有缘人)
     *
     * @param list
     * @return
     */
    public static List<ChartElectricityInfoVO> electricityDataDetail(List<ChartElectricityInfoVO> list) {
        List<String> temp = new ArrayList<String>();
        if (CollUtil.isNotEmpty(list)) {
            for (int i = 0; i < list.size() - 1; i++) {
                BigDecimal last = new BigDecimal(list.get(i).getElectricity());
                BigDecimal now = new BigDecimal(list.get(i + 1).getElectricity());
                temp.add(now.subtract(last).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            for (int i = 1; i < list.size(); i++) {
                list.get(i).setElectricity(temp.get(i - 1));
            }
            if (Double.parseDouble(list.get(list.size() - 1).getElectricity()) <= 0) {
                list.remove(list.size() - 1);
            }
        }
        return list;
    }

    /**
     * 获取电站统计数据信息
     *
     * 分页查询电站的详细统计信息
     *
     * @param query 电站统计查询条件，包含分页、排序、筛选等参数
     * @param userInfo 用户权限信息
     * @return 分页的电站统计信息列表
     */
    @Override
    public Page<PlantStatisticsInfoVO> getPlantStatisticsInfo(PlantStatisticsQueryDTO query, RequireParamsDTO userInfo) {
        Page<PlantStatisticsInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        String dateType = "";
        if (query.getPowerStartTime() != null && query.getPowerEndTime() != null) {
            int startlength = query.getPowerStartTime().length();
            int endlength = query.getPowerEndTime().length();
            if (startlength == 0 && endlength == 0) {
                dateType = "all";
            } else if (startlength == 10 && endlength == 10) {
                dateType = "day";
            } else if (startlength == 7 && endlength == 7) {
                dateType = "month";
            } else if (startlength == 4 && endlength == 4) {
                dateType = "year";
            } else {
                throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED.getCode(),
                        ResultEnum.DATETIME_FORMAT_FAILED.getMessage());
            }
        }
        // 条件查询电站Uid
        IPage<PlantStatisticsInfoVO> plantPage = plantStatisticsMapper.selectPlantUidList(query, page, userInfo, dateType);
        List<String> plantUidArray = plantPage.getRecords().stream().map(PlantStatisticsInfoVO::getPlantUid).collect(Collectors.toList());
        if (plantUidArray.size() <= 0) {
            return page;
        }
        // 通过电站Uid数组查询电站统计数据
        List<PlantStatisticsInfoVO> plantBaseInfoList = plantStatisticsMapper.getPlantBaseInfo(query, plantUidArray, dateType);
        // 通过电站Uid数组查询电站告警数量
        List<PlantStatisticsInfoVO> PlantAlarmNumList = plantStatisticsMapper.getPlantAlarmNum(query, plantUidArray, dateType);
        List<PlantInfoVO> plantStatusInfo = plantStatisticsMapper.getPlantStatusInfo(plantUidArray);
        for (PlantStatisticsInfoVO plantStatisticsInfoVO : plantBaseInfoList) {
            String projectSpecial = plantStatisticsInfoVO.getProjectSpecial();
            String substring = projectSpecial.substring(0, 1);
            String result =
                    "1".equals(substring) ? "户用" :
                    "2".equals(substring) ? "整县" :
                    "3".equals(substring) ? "工商业" :
                    "4".equals(substring) ? "户租" :
                    "5".equals(substring) ? "产品服务" :
                    "6".equals(substring) ? "储能" :
                    "未知类型";

            plantStatisticsInfoVO.setProjectType(result);
            for (PlantInfoVO plantInfoVO : plantStatusInfo) {
                if (plantStatisticsInfoVO.getPlantUid().equals(plantInfoVO.getPlantUid())) {
                    plantStatisticsInfoVO.setPlantType(plantInfoVO.getPlantType());
                    plantStatisticsInfoVO.setStatus(plantInfoVO.getPlantStatus());
                    plantStatisticsInfoVO.setPowerDistributor(plantInfoVO.getPowerDistributor());
                    plantStatisticsInfoVO.setProjectName(plantInfoVO.getProjectName());
                }
            }
        }
        for (int i = 0, j = 0; i < PlantAlarmNumList.size() && j < plantBaseInfoList.size(); i++) {
            if (PlantAlarmNumList.get(i).getPlantUid().equals(plantBaseInfoList.get(j).getPlantUid())) {
                plantBaseInfoList.get(j).setAlarmNum(PlantAlarmNumList.get(i).getAlarmNum());
            } else {
                plantBaseInfoList.get(j).setAlarmNum("0");
                i--;
            }
            j++;
        }
        setPlantStatics(plantBaseInfoList);
        Page<PlantStatisticsInfoVO> result = page.setRecords(plantBaseInfoList);
        return result;
    }

    /**
     * 设置电站统计信息
     *
     * 计算并设置电站的等效利用小时数和收益等统计信息
     *
     * @param plantBaseInfoList 电站基本信息列表
     */
    private static void setPlantStatics(List<PlantStatisticsInfoVO> plantBaseInfoList) {
        for (int i = 0; i < plantBaseInfoList.size(); i++) {
            BigDecimal electricity = new BigDecimal(plantBaseInfoList.get(i).getElectricity());
            BigDecimal plantCapacity = new BigDecimal(plantBaseInfoList.get(i).getPlantCapacity());
            BigDecimal electricityPrice = new BigDecimal(plantBaseInfoList.get(i).getElectricityPrice());
            if ("0".equals(plantBaseInfoList.get(i).getStatus())) {
                Date updateTime = DateUtil.parseDate(plantBaseInfoList.get(i).getUpdateTime());
                long betweenMS = DateUtil.between(updateTime, DateUtil.date(), DateUnit.MS);
                plantBaseInfoList.get(i).setOfflineTime(DateUtil.formatBetween(betweenMS, BetweenFormatter.Level.MINUTE));
            }
            // 等效小时=(electricity/100)/(plant_capacity/1000)
            BigDecimal equivalentUseHour = electricity.divide(plantCapacity, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
            plantBaseInfoList.get(i).setEquivalentUseHour(equivalentUseHour.toString());
            // 收益=electricity/100*sale_price
            BigDecimal income = electricity.multiply(electricityPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
            plantBaseInfoList.get(i).setIncome(income.toString());
        }
    }

    /**
     * 获取电站等效利用小时数分布
     *
     * 统计指定条件下电站的等效利用小时数分布情况，按5个区间进行分布统计
     * 用于评估电站的发电效率和使用情况
     *
     * @param query 电站统计查询条件
     * @param userInfo 用户权限信息
     * @param projectId 项目ID，可为空
     * @return 等效小时数分布Map，key为区间范围，value为对应区间的电站数量
     */
    @Override
    public Map<String, Integer> getPlantEquivalentUseHour(PlantStatisticsQueryDTO query, RequireParamsDTO userInfo, String projectId) {
        query.setProjectId(projectId);
        String dateType = "";
        if (query.getPowerStartTime() != null && query.getPowerEndTime() != null) {
            int startlength = query.getPowerStartTime().length();
            int endlength = query.getPowerEndTime().length();
            if (startlength == 0 && endlength == 0) {
                dateType = "all";
            } else if (startlength == 10 && endlength == 10) {
                dateType = "day";
            } else if (startlength == 7 && endlength == 7) {
                dateType = "month";
            } else if (startlength == 4 && endlength == 4) {
                dateType = "year";
            } else {
                throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED.getCode(),
                        ResultEnum.DATETIME_FORMAT_FAILED.getMessage());
            }
        }
        // 通过电站Uid数组查询电站统计数据
        List<PlantStatisticsInfoVO> plantBaseInfoList = plantStatisticsMapper.getPlantBaseInfo(query, null, dateType);

        setPlantStatics(plantBaseInfoList);

        if (CollUtil.isEmpty(plantBaseInfoList)) {
            return Collections.emptyMap();
        }
        List<String> equivalentUseHourList = plantBaseInfoList.stream()
                .map(PlantStatisticsInfoVO::getEquivalentUseHour)
                .sorted(Comparator.comparing(BigDecimal::new))
                .collect(Collectors.toList());

        String lastNum = equivalentUseHourList.get(equivalentUseHourList.size() - 1);

        Map<String, BigDecimal[]> complianceRanges = RangeUtils.generateComplianceRanges(5, lastNum);

        return RangeUtils.getCountMap(plantBaseInfoList, PlantStatisticsInfoVO::getEquivalentUseHour, complianceRanges);
    }

    /**
     * 查询统计数据的入口方法
     * 此方法实现了策略模式的分发逻辑，根据请求类型选择对应的处理策略
     *
     * @param dto 包含查询参数的数据传输对象
     * @return 包装了统计结果的响应对象
     */
    @Override
    public Result<?> queryStatistics(StatisticsQueryDTO dto) {
        // 1. 根据请求的type从策略映射中获取对应的处理函数
        Function<StatisticsQueryDTO, Object> handler = statisticsHandlers.get(dto.getType());
        // 2. 验证请求类型的有效性
        if (handler == null) {
            throw new BusinessException("Invalid type");
        }
        // 3. 调用选定的处理函数处理请求
        Object result = handler.apply(dto);
        return Result.success(result);
    }

    /**
     * 处理发电统计请求
     *
     * @param dto 查询参数
     * @return 发电统计结果
     */
    private Object handleGenerationStatistics(StatisticsQueryDTO dto) {
        dto.setTableName(getInverterTableName(dto));
        return getStatisticsResult(dto, this::selectGenerationStatistics);
    }

    /**
     * 处理用电统计请求
     *
     * @param dto 查询参数
     * @return 用电统计结果
     */
    private Object handleLoadStatistics(StatisticsQueryDTO dto) {
        dto.setTableName(getBatteryTableName(dto));
        return getStatisticsResult(dto, this::selectLoadStatistics);
    }

    /**
     * 处理储电统计请求
     *
     * @param dto 查询参数
     * @return 储电统计结果
     */
    private Object handleBatteryStatistics(StatisticsQueryDTO dto) {
        dto.setTableName(getBatteryTableName(dto));
        return getStatisticsResult(dto, this::selectBatteryStatistics);
    }

    /**
     * 处理收益统计请求
     *
     * @param dto 查询参数
     * @return 收益统计结果
     */
    private Object handleRevenueStatistics(StatisticsQueryDTO dto) {
        if (dto.getInverterSns().size() > 1 && StrUtil.isEmpty(dto.getInverterSn())) {
            // todo 工商业/多逆变器
            return null;
        } else {
            // 户用/单逆变器
            return getStatisticsResult(dto, dateType -> statisticsMapper.queryRevenueStatistics(getThreadLocalDTO()));
        }
    }

    /**
     * 统一处理各种统计类型的日期查询逻辑
     * 该方法提取了共同的逻辑，减少了代码重复
     *
     * @param dto           查询参数
     * @param queryFunction 根据日期类型执行不同查询的函数
     * @return 查询结果
     */
    private Object getStatisticsResult(StatisticsQueryDTO dto, Function<String, Object> queryFunction) {
        String dateType = dto.getDateType();
        if (CollUtil.isNotEmpty(dto.getInverterSns())) {
            if (dto.getInverterSns().size() == 1) {
                dto.setInverterSn(dto.getInverterSns().get(0));
            }
        } else {
            throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR);
        }
        try {
            // 将当前DTO设置到ThreadLocal中
            CURRENT_DTO.set(dto);
            return queryFunction.apply(dateType);
        } finally {
            // 使用完后清理ThreadLocal
            CURRENT_DTO.remove();
        }
    }

    // 获取当前线程的DTO对象
    private StatisticsQueryDTO getThreadLocalDTO() {
        return CURRENT_DTO.get();
    }

    // 选择发电统计的查询方法
    private Object selectGenerationStatistics(String dateType) {
        StatisticsQueryDTO dto = getThreadLocalDTO();
        if (dto.getInverterSns().size() > 1 && StrUtil.isEmpty(dto.getInverterSn())) {
            // todo 工商业/多逆变器
            return null;
        } else {
            // 户用/单逆变器
            if (DATE_TYPE_DAY.equals(dateType)) {
                // 获取指定日期发电量
                HashMap<String, Object> hashMap = new HashMap<>();
                String electricity = statisticsMapper.selectGenerationDay(dto);
                hashMap.put("electricity", electricity);
                hashMap.put("list", statisticsMapper.selectGenerationPowerDay(dto));
                return hashMap;
            } else {
                return statisticsMapper.selectGenerationEnergy(dto);
            }
        }
    }

    // 选择用电统计的查询方法
    private Object selectLoadStatistics(String dateType) {
        StatisticsQueryDTO dto = getThreadLocalDTO();
        if (dto.getInverterSns().size() > 1 && StrUtil.isEmpty(dto.getInverterSn())) {
            // todo 工商业/多逆变器
            return null;
        } else {
            // 户用/单逆变器
            if (DATE_TYPE_DAY.equals(dateType)) {
                HashMap<String, Object> hashMap = new HashMap<>();
                // 获取指定日期用电量
                Map<String, Object> stringObjectMap = statisticsMapper.selectLoadDay(dto);
                hashMap.put("day", dto.getDate());
                hashMap.put("electricity", stringObjectMap.get("loadEnergy"));
                hashMap.put("selfEnergy", stringObjectMap.get("selfEnergy"));
                hashMap.put("sellEnergy", stringObjectMap.get("sellEnergy"));
                hashMap.put("list", statisticsMapper.selectLoadPowerDay(dto));
                return hashMap;
            } else {
                return statisticsMapper.selectLoadEnergy(dto);
            }
        }
    }

    // 选择储电统计的查询方法
    private Object selectBatteryStatistics(String dateType) {
        StatisticsQueryDTO dto = getThreadLocalDTO();
        if (dto.getInverterSns().size() > 1 && StrUtil.isEmpty(dto.getInverterSn())) {
            // todo 工商业/多逆变器
            return null;
        } else {
            // 户用/单逆变器
            if (DATE_TYPE_DAY.equals(dateType)) {
                // 获取充放电量
                PlantAllStatisticsVO.BatteryEnergyVO batteryPowerVO = statisticsMapper.selectBatteryDay(dto);
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("list", statisticsMapper.selectBatteryPowerDay(dto));
                hashMap.put("chargeEnergy", batteryPowerVO.getChargeEnergy());
                hashMap.put("dischargeEnergy", batteryPowerVO.getDischargeEnergy());
                return hashMap;
            } else {
                return statisticsMapper.selectBatteryEnergy(dto);
            }
        }
    }

    // 获取电池表名
    private String getBatteryTableName(StatisticsQueryDTO dto) {
        if (StrUtil.isNotEmpty(dto.getDate())) {
            String replace = dto.getDate().replace("-", "");
            if (DATE_TYPE_DAY.equals(dto.getDateType())) {
                return TableUtils.getTableName("bto_battery_", replace);
            }
        }
        return TableUtils.getTableName("bto_battery_", DateUtil.today());
    }

    // 获取逆变器表名
    private String getInverterTableName(StatisticsQueryDTO dto) {
        if (StrUtil.isNotEmpty(dto.getDate())) {
            String replace = dto.getDate().replace("-", "");
            if (DATE_TYPE_DAY.equals(dto.getDateType())) {
                return TableUtils.getTableName("bto_inverter_", replace);
            }
        }
        return TableUtils.getTableName("bto_inverter_", DateUtil.today());
    }
}
