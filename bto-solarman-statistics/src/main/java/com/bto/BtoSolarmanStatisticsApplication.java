package com.bto;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date ${DATE} ${TIME}
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan(basePackages = {"com.bto.*.dao"})
@EnableFeignClients(basePackages = {"com.bto.api.feign*"})
public class BtoSolarmanStatisticsApplication {
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(BtoSolarmanStatisticsApplication.class);
        springApplication.run(args);
    }
}