<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.statistics.dao.PlantStatisticsMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <sql id="getInfoByUser">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bto_plant_base.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bto_plant_base.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <sql id="getEveryHourElectricityInfoByUser">
        SELECT sum(today_electricity) electricity, concat(LEFT(init_time, 14), '00:00') data_time
        FROM bto_inverter_${tableSuffix} t1
        WHERE EXISTS(SELECT device_id
        FROM bto_device
        <where>
            is_deleted = 0
            AND device_type = 1
            AND
            plant_uid IN
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
            AND device_id = t1.inverter_sn)
        </where>
        GROUP BY data_time
    </sql>
    <sql id="getEveryHourElectricityInfoByEnterprise">
        SELECT data_time, SUM(electricity) electricity
        FROM `bto_curdate_electricity`
        <where>
            project_special IN
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </where>
        GROUP BY data_time
    </sql>

    <resultMap id="ElectricityStatisticsInfoMap" type="com.bto.commons.pojo.vo.ElectricityStaticsInfoVO">
        <id property="plantUid" column="plantUid" jdbcType="VARCHAR"/>
        <result property="plantName" column="plantName" jdbcType="VARCHAR"/>
        <collection property="electricityList" ofType="com.bto.commons.pojo.vo.ElectricityInfoVO"
                    javaType="java.util.ArrayList">
            <result property="electricity" column="electricity" javaType="String"/>
            <result property="dataTime" column="dataTime" javaType="String"/>
            <result property="electricityEfficiency" column="electricityEfficiency" javaType="String"/>
            <result property="plantPrice" column="plantPrice" javaType="String"/>
            <result property="electricityEfficiency" column="electricityEfficiency" javaType="String"/>
            <result property="plantCapacity" column="plantCapacity" javaType="String"/>
        </collection>
    </resultMap>

    <!--按天数获取发电量数据-->
    <select id="getElectricityByNumDay" parameterType="com.bto.commons.pojo.vo.RequireParamsDTO"
            resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT SUM(electricity) electricity, collect collectDate
        FROM
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            bto_project_day
        </if>
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            bto_plant_day
        </if>
        <where>
            <include refid="userInfo"/>
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
        LIMIT #{nums}
    </select>
    <!--获取近六个月发电量数据-->
    <select id="getElectricityBySixMonth" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT SUM(electricity) electricity, LEFT(collect, 7) collectDate
        FROM
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            bto_project_day
        </if>
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            bto_plant_day
        </if>
        <where>
            <include refid="userInfo"/>
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
        LIMIT 6;
    </select>
    <select id="getPlantElectricityInfo" resultType="com.bto.commons.pojo.vo.PlantElectricityVO">
        SELECT SUM(power) totalPower,
        SUM(today_electricity) todayElectricity,
        SUM(month_electricity) monthElectricity,
        SUM(year_electricity) yearElectricity,
        SUM(total_electricity) totalElectricity,
        SUM(plant_capacity) plantCapacity,
        SUM(today_electricity) todayCo2,
        SUM(total_electricity) totalCocal,
        SUM(total_electricity) totalCo2,
        SUM(total_electricity) treeNum
        FROM bto_plant_base
        <where>
            is_deleted = '0'
            <include refid="userInfo"/>
        </where>
    </select>
    <select id="getPlantNumInfo" resultType="com.bto.commons.pojo.vo.NumInfoVO">
        SELECT plant_status status, count(*) statusNum
        FROM bto_plant_base
        <where>is_deleted = '0'
            <include refid="userInfo"/>
        </where>
        GROUP BY plant_status
    </select>

    <select id="getElectricityStatisticsInfoByDay"
            parameterType="com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO"
            resultMap="ElectricityStatisticsInfoMap">
        SELECT
        t1.plant_uid plantUid,
        t1.plant_name plantName,
        SUM(t2.today_electricity)/(t1.plant_capacity*3.5) electricityEfficiency,
        t2.init_time dataTime,
        SUM(t2.today_electricity) electricity,
        t1.sale_price plantPrice,
        t1.plant_capacity plantCapacity
        FROM v_plant_inverter t1
        LEFT JOIN ${tableName} t2 ON t2.inverter_sn = t1.device_id
        <where>
            <if test="plantUidArray.size()>0 and plantUidArray!=null">
                t1.plant_uid in
                <foreach collection="plantUidArray" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid,t2.init_time
    </select>
    <select id="getElectricityStatisticsInfoByMonth" resultMap="ElectricityStatisticsInfoMap">
        SELECT t1.plant_uid plantUid,
        t1.collect dataTime,
        SUM(t1.electricity) electricity,
        SUM(t1.electricity)/(t2.plant_capacity*3.5) electricityEfficiency,
        t2.plant_name plantName,
        t2.sale_price plantPrice,
        t2.plant_capacity plantCapacity
        FROM bto_plant_day t1
        LEFT JOIN bto_plant_base t2 ON t1.plant_uid = t2.plant_uid
        <where>
            LEFT(collect,7) = #{query.date} AND
            <if test="plantUidArray.size()>0 and plantUidArray!=null">
                t1.plant_uid IN
                <foreach collection="plantUidArray" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid,t1.collect
    </select>
    <select id="getElectricityStatisticsInfoByYear"
            resultMap="ElectricityStatisticsInfoMap">
        SELECT t1.plant_uid plantUid,
        LEFT(t1.collect,7) dataTime,
        SUM(t1.electricity) electricity,
        SUM(t1.electricity)/(t2.plant_capacity*3.5) electricityEfficiency,
        t2.plant_name plantName,
        t2.sale_price plantPrice,
        t2.plant_capacity plantCapacity
        FROM bto_plant_day t1
        LEFT JOIN bto_plant_base t2 ON t1.plant_uid = t2.plant_uid
        <where>
            LEFT(collect,4) = #{query.date} AND
            <if test="plantUidArray.size()>0 and plantUidArray!=null">
                t1.plant_uid IN
                <foreach collection="plantUidArray" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid,dataTime
    </select>

    <select id="getWorkEfficiencyInfo" resultType="com.bto.commons.pojo.vo.PlantElectricityVO">
        SELECT plant_name plantName,
        (POWER / plant_capacity) * 100 maxEfficiency,
        (SELECT avg(workEfficiency) * 100 averageEfficiency
        FROM (
        SELECT plant_name, POWER / plant_capacity workEfficiency
        FROM bto_plant_base
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
        </where>
        ORDER BY workEfficiency DESC) t1
        ) averageEfficiency
        FROM bto_plant_base
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
        </where>
        ORDER BY maxEfficiency DESC
        LIMIT 1;
    </select>
    <select id="getEveryHourElectricityInfo" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT ABS(IFNULL(electricity - LAG(electricity) OVER (ORDER BY data_time), 0)) AS electricity,
        data_time collectDate
        FROM (
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            <include refid="getEveryHourElectricityInfoByEnterprise"/>
        </if>
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            <include refid="getEveryHourElectricityInfoByUser"/>
        </if>
        ) t
    </select>


    <select id="selectPlantUidList" resultType="com.bto.commons.pojo.vo.PlantStatisticsInfoVO">
        SELECT
        bto_plant_base.plant_uid plantUid
        FROM
        bto_plant_base
        LEFT JOIN v_user_plant ON bto_plant_base.plant_uid = v_user_plant.plant_uid
        <where>
            bto_plant_base.is_deleted = '0'
            AND bto_plant_base.inverter_num > 0
            <include refid="getInfoByUser"/>
            <if test="query.plantName!=null and query.plantName!='' ">
                AND bto_plant_base.plant_name LIKE concat('%',#{query.plantName},'%')
            </if>
            <if test="query.plantStatus!=null and query.plantStatus.size() > 0">
                AND v_user_plant.plant_status IN
                <foreach collection="query.plantStatus" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
            <if test="query.powerDistributor!=null and query.powerDistributor!='' ">
                AND v_user_plant.power_distributor = #{query.powerDistributor}
            </if>
            <if test="query.address!=null and query.address!='' ">
                AND bto_plant_base.address LIKE concat('%',#{query.address},'%')
            </if>
            <if test="query.createStartTime!=null and query.createStartTime!=''
            and query.createEndTime!=null and query.createEndTime!='' ">
                AND bto_plant_base.create_time BETWEEN #{query.createStartTime} AND
                date_add(#{query.createEndTime},interval 1 day)
            </if>
            <if test="query.powerStartTime!=null and query.powerStartTime!=''
            and query.powerEndTime!=null and query.powerEndTime!='' ">
                <if test="dateType !=null and dateType == 'day' ">
                    AND left(bto_plant_base.create_time,10) &lt;= #{query.powerEndTime}
                </if>
                <if test="dateType !=null and dateType == 'month' ">
                    AND left(bto_plant_base.create_time,7) &lt;= #{query.powerEndTime}
                </if>
                <if test="dateType !=null and dateType == 'year' ">
                    AND left(bto_plant_base.create_time,4) &lt;= #{query.powerEndTime}
                </if>

            </if>
        </where>
        ORDER BY bto_plant_base.create_time DESC
    </select>

    <select id="getPlantBaseInfo" parameterType="com.bto.commons.pojo.dto.PlantStatisticsQueryDTO"
            resultType="com.bto.commons.pojo.vo.PlantStatisticsInfoVO">
        SELECT
        v.meter_id meterID,
        v.plant_uid plantUid,
        v.plant_name plantName,
        v.user_name userName,
        v.plant_capacity plantCapacity,
        v.total_electricity totalElectricity,
        v.address,
        v.project_special projectSpecial,
        v.city,
        v.inverter_num inverterNum,
        v.create_time createTime,
        v.receive_time updateTime,
        v.plant_status STATUS,
        v.sale_price electricityPrice,
        v.assurance_level assuranceLevel,
        SUM( t.electricity ) electricity,
        SUM( t.electricity ) totalReduceCo2,
        SUM( t.electricity ) totalPlantTreeNum
        FROM
        v_user_plant v
        LEFT JOIN bto_plant_day t ON t.plant_uid = v.plant_uid
        <where>
            <if test="dateType!='' and dateType =='year'">
                (LEFT(t.collect,4) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='month'">
                (LEFT(t.collect,7) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='day'">
                (LEFT(t.collect,10) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="plantUidArray !=null and plantUidArray.size()>0 ">
                AND t.plant_uid IN
                <foreach collection="plantUidArray" open="(" close=")" separator="," item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
            <if test="query.projectId != null and query.projectId != '0' and query.projectId != ''">
                AND v.project_special LIKE CONCAT(#{query.projectId}, '%')
            </if>
        </where>
        GROUP BY v.plant_uid
        ORDER BY v.create_time DESC
    </select>
    <select id="getPlantAlarmNum" resultType="com.bto.commons.pojo.vo.PlantStatisticsInfoVO">
        SELECT
        t1.plant_uid plantUid,
        count(t2.inverter_sn ) alarmNum
        FROM
        bto_inverter_latest t1 LEFT JOIN bto_plant_base t ON t.plant_uid = t1.plant_uid
        LEFT JOIN bto_inverter_alarm t2 ON t1.inverter_sn = t2.inverter_sn
        <where>
            AND t.inverter_num &lt;&gt; 0
            <if test="dateType!='' and dateType =='year'">
                AND (LEFT(t2.alarm_date,4) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='month'">
                AND (LEFT(t2.alarm_date,7) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='day'">
                AND (LEFT(t2.alarm_date,10) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="plantUidArray.size()>0">
                AND t1.plant_uid IN
                <foreach collection="plantUidArray" open="(" close=")" separator="," item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid
        ORDER BY t.create_time DESC
    </select>
    <select id="getElectricityAnalyzeDataByDay" parameterType="com.bto.commons.pojo.dto.ElectricityAnalyzeDataDTO"
            resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT electricity electricity,
               collect     collectDate
        FROM bto_plant_day
        WHERE plant_uid = #{plantUid}
          AND collect BETWEEN #{startTime} AND #{endTime}
        GROUP BY collect
        ORDER BY collect DESC
    </select>
    <select id="getWeatherAnalyzeData" resultType="com.bto.commons.pojo.vo.WeatherInfoVO">
        SELECT city,
               weather,
               temp temperature,
               wind,
               windscale,
               windspeed,
               humidity,
               precip,
               pressure,
               cloud,
               time
        FROM ${tableName}
        WHERE city = #{city}
          AND time BETWEEN #{startTime} AND #{endTime}
    </select>
    <select id="getSpaceAnalyzeData" resultType="com.bto.commons.pojo.vo.SpaceAnalyzeDataVO">
        SELECT v.plant_uid      plantUid,
               sum(t1.`power`)  totalPower,
               v.plant_capacity plantCapacity,
               t1.init_time     dataTime
        FROM ${tableName} t1
                 LEFT JOIN v_plant_inverter v ON
            t1.inverter_sn = v.device_id
        WHERE v.plant_uid = #{plantUid}
        GROUP BY t1.init_time
        ORDER BY t1.init_time DESC
    </select>
    <select id="getFaultStatisticsData" resultType="com.bto.commons.pojo.vo.FaultStatisticsDataVO">
        SELECT v2.plant_uid          plantUid,
               v1.alarm_level        alarmLevel,
               COUNT(v1.alarm_level) nums
        FROM v_inverter_alarm v1
                 LEFT JOIN v_plant_inverter v2 ON v1.inverter_sn = v2.device_id
        WHERE v1.inverter_sn IN
              (SELECT device_id FROM bto_device WHERE plant_uid = #{plantUid})
        GROUP BY v1.alarm_level
    </select>

    <select id="getElectricityBySevenDay" resultType="com.bto.commons.pojo.vo.BatteryDivinerVO">
        SELECT collect     collectDate,
               electricity predictElectricity
        FROM bto_plant_day
        WHERE plant_uid = #{plantUid}
        ORDER BY collect DESC
        LIMIT 7
    </select>
    <select id="selectAllPlantUidList" resultType="string">
        WITH w_pid AS(
        WITH RECURSIVE project_hierarchy AS (
        SELECT id, pid
        FROM bto_project_category
        WHERE is_deleted = 0 AND pid = #{projectId}
        UNION ALL
        SELECT c.id, c.pid
        FROM bto_project_category c
        JOIN project_hierarchy p ON c.pid = p.id
        WHERE c.is_deleted = 0
        )
        SELECT id FROM project_hierarchy
        UNION
        SELECT id FROM bto_project_category WHERE is_deleted = 0 AND id = #{projectId}
        )
        SELECT
        plant_uid plantUid
        FROM
        bto_plant_base bpb
        <where>
            bpb.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="projectId !='' and projectId !=null">
                and bpb.project_special IN ( SELECT id FROM w_pid)
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    <select id="getPlantMassByProjectId" resultType="string">
        SELECT
        SUM( plant_abnormal_num ) / SUM( plant_num ) AS failureRate
        FROM
        bto_project_day
        WHERE
        project_special IN
        <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
            #{projectID}
        </foreach>
        AND collect = #{date}
    </select>
    <select id="getPlantMassByPlantList" resultType="java.lang.Integer">
        SELECT
        count(*) as statusNum
        FROM
        bto_device
        <where>
            is_deleted = 0
            AND `status` = '2'
            <include refid="userInfo"/>
        </where>
        GROUP BY
        `status`;
    </select>
    <select id="getPlantStatusInfo" resultType="com.bto.commons.pojo.vo.PlantInfoVO">
        SELECT
        plant_uid plantUid,
        plant_status plantStatus,
        plant_type_id plantType,
        power_distributor powerDistributor,
        LEFT ( project_special, 1 ) projectName
        FROM
        v_user_plant
        WHERE
        plant_uid IN
        <foreach collection="plantUid" open="(" close=")" separator="," item="plantUid">
            #{plantUid}
        </foreach>
    </select>
    <select id="exportLowPowerPlant" resultType="com.bto.statistics.pojo.LowPowerPlantVO">
        WITH plant_efficiency AS (
        SELECT
        t1.city,
        t1.project_special,
        t1.plant_uid,
        t1.plant_name,
        t1.plant_capacity,
        t1.total_electricity / 100 as total_electricity,
        t1.plant_status status,
        t1.create_time,
        t2.electricity/100 AS today_electricity,
        ROUND( ((t2.electricity) / 100) / (t1.plant_capacity / 1000),4) AS equivalent_hours
        FROM bto_plant_base t1
        LEFT JOIN bto_plant_day t2
        ON t1.plant_uid = t2.plant_uid
        AND t2.collect = #{date}
        <where>
            t1.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="scope !=null and scope.size()>0 ">
                AND t1.project_special IN
                <foreach collection="scope" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid, t1.plant_name, t1.city, t1.plant_capacity, t1.project_special
        ),
        city_average AS (
        SELECT
        city,
        ROUND(SUM(equivalent_hours) / COUNT(*), 4) AS city_avg_hours
        FROM plant_efficiency
        GROUP BY city
        )
        SELECT
        p.city,
        p.project_special,
        p.plant_uid,
        p.plant_name,
        p.plant_capacity,
        p.total_electricity,
        p.status,
        p.create_time,
        p.today_electricity,
        p.equivalent_hours,
        c.city_avg_hours,
        ROUND(p.equivalent_hours / c.city_avg_hours,4) AS city_generation_rate
        FROM plant_efficiency p
        JOIN city_average c ON p.city = c.city
        WHERE p.equivalent_hours &lt; c.city_avg_hours
                                   ORDER BY p.city, p.equivalent_hours;

    </select>
</mapper>