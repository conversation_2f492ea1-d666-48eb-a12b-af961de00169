<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.statistics.dao.AlarmStatisticMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <select id="getAlarmInfoNum" resultType="java.lang.Integer">
        -- SELECT COUNT(1)
        -- FROM bto_inverter_alarm t1
        -- where status = '0' AND alarm_info NOT LIKE'PV%'
        -- AND
        -- EXISTS
        -- (SELECT device_id
        -- FROM bto_device
        -- WHERE is_deleted = 0
        -- AND device_type = 1
        -- AND device_id = t1.inverter_sn
        SELECT count(*) from v_plant_alarm
        <where>
            <include refid="userInfo"/>
        </where>
    </select>
    <select id="getAlarmPlantNum" resultType="java.lang.Integer">
        SELECT count(1) FROM bto_plant_base
        <where>
            is_deleted = '0' and plant_status = '2'
            <include refid="userInfo"/>
        </where>
    </select>
</mapper>