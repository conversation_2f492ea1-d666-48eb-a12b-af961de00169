<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.statistics.dao.GenerateElectricityOperationMapper">

    <select id="getProjectIdByPid" resultType="com.bto.commons.pojo.vo.ProjectInfoVO">
        SELECT
        id,
        name projectName,
        pid,
        create_time createTime
        FROM bto_project_category
        <where>
            <foreach collection="pidList" item="pid" separator="or">
                id LIKE concat(#{pid},'%')
            </foreach>
        </where>
    </select>
    <select id="getAnalyzeObjectData" resultType="com.bto.commons.pojo.vo.AnalyzeObjectDataVO">
        SELECT plant_uid plantUid,
        plant_name plantName,
        plant_capacity plantCapacity,
        power,
        orientation,
        plant_status plantStatus,
        inverter_num,
        today_electricity todayElectricity,
        month_electricity,
        year_electricity,
        total_electricity totalElectricity,
        create_time createTime,
        receive_time updateTime
        FROM bto_plant_base
        <where>
            is_deleted = 0
            AND inverter_num != '0'
            <if test="query.useYears!=null and query.useYears!=''">
                AND TIMESTAMPDIFF(YEAR, create_time, now()) = #{query.useYears}
            </if>
            <if test="query.isAlarm">
                AND plant_status = '2'
            </if>
            <if test="!query.isAlarm">
                AND plant_status != '2'
            </if>
            <if test="query.projectTypeList.size>0">
                AND
                project_special IN
                <foreach collection="query.projectTypeList" item="projectType" index="item" separator="," open="("
                         close=")">
                     #{projectType}
                </foreach>
            </if>
            <if test="query.createStartTime!=null and query.createEndTime != null
        and query.createStartTime!='' and query.createEndTime != ''">
                AND create_time between #{query.createStartTime} and #{query.createEndTime}
            </if>
            <if test="query.plantStatus!=null and query.plantStatus != ''">
                AND plant_status = #{query.plantStatus}
            </if>
            <if test="query.province!=null and query.province!=''">
                AND province = #{query.province}
            </if>
            <if test="query.city!=null and query.city!=''">
                AND city =#{query.city}
            </if>
            <if test="query.town!=null and query.town!=''">
                AND town = #{query.town}
            </if>
        </where>

    </select>
</mapper>