<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.statistics.dao.DeviceStatisticMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <select id="getTotalDeviceNum" resultType="java.lang.Integer">
        SELECT count(1)
        FROM bto_device
        <where>
            is_deleted = '0'
            <include refid="userInfo"/>
        </where>
    </select>
    <select id="getDeviceStatusNumInfo" resultType="com.bto.commons.pojo.vo.NumInfoVO">
        SELECT `status`,
        count(*) statusNum
        FROM bto_device
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
        </where>
        GROUP BY `status`
    </select>
</mapper>