<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.statistics.dao.IntegrativeStatisticMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>

    <sql id="userInfoWithBpb">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <resultMap id="projectInspectionMap" type="com.bto.commons.pojo.vo.ProjectInspectionInfoVO">
        <result column="plantType" property="plantType"/>
        <collection property="projectInspectionInfoList" ofType="com.bto.commons.pojo.vo.ProjectInspectionInfoVO">
            <result column="city" property="city"/>
            <collection property="projectInspectionInfoList" ofType="com.bto.commons.pojo.vo.ProjectInspectionInfoVO">
                <result column="projectId" property="projectId"/>
                <result column="plantNum" property="plantNum"/>
                <result column="plantCapacity" property="plantCapacity"/>
                <result column="electricity" property="electricity"/>
                <result column="alarmPlantNum" property="alarmPlantNum"/>
            </collection>
        </collection>
    </resultMap>
    <select id="getMultipleMonthElectricity" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT SUM(electricity) electricity,
        LEFT(collect, 7) collectDate,
        SUM(electricity)*#{query.electricityPrice} income,
        SUM(plant_capacity) plantCapacity
        FROM
        bto_project_day
        <where>
            <include refid="userInfo"/>
            AND collect between #{query.dataStartTime} and #{query.dataEndTime}
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC

    </select>
    <select id="multipleDayElectricity" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT SUM(electricity) electricity,
        LEFT(collect, 10) collectDate,
        SUM(plant_capacity) plantCapacity,
        SUM(electricity)*#{query.electricityPrice} income
        FROM
        bto_project_day
        <where>
            <include refid="userInfo"/>
            AND collect between #{query.dataStartTime} and #{query.dataEndTime}
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
    </select>
    <select id="efficiencyPerHourByMultipleDay" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT
        SUM(electricity) electricity,
        SUM(plant_capacity) plantCapacity,
        SUM(electricity)/ SUM(plant_capacity) income,
        SUM(plant_num) plantNum,
        SUM(plant_abnormal_num) abnormalNum,
        SUM(plant_num)-SUM(plant_abnormal_num) AS normalNum,
        LEFT(collect, 10) collectDate
        FROM bto_project_day
        <where>
            <include refid="userInfo"/>
            AND collect between #{query.dataStartTime} and #{query.dataEndTime}
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
    </select>
    <select id="getIntegrativeStatisticChart"
            resultType="com.bto.commons.pojo.vo.IntegrativeStatisticChartVO">
        SELECT
        SUM(electricity) electricity,
        SUM(plant_capacity) plantCapacity,
        SUM(electricity)/ SUM(plant_capacity) income,
        SUM(plant_num) plantNum,
        SUM(plant_abnormal_num) abnormalPlantNum,
        SUM(plant_num)-SUM(plant_abnormal_num) AS normalPlantNum,
        <if test="isMonth">
            LEFT(collect, 7) collectDate
        </if>
        <if test="!isMonth">
            LEFT(collect, 10) collectDate
        </if>
        FROM bto_project_day
        <where>
            <include refid="userInfo"/>
            AND collect between #{query.dataStartTime} and #{query.dataEndTime}
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
    </select>
    <select id="getIntegrativeStatisticSheet"
            resultType="com.bto.commons.pojo.vo.IntegrativeStatisticSheetVO">
        SELECT SUM(plant_capacity) plantCapacity,
        SUM(power) power,
        COUNT(*) plantNum,
        SUM(total_electricity) electricity,
        SUM(total_electricity) income,
        SUM(total_electricity) reduceCo2,
        SUM(total_electricity) reduceCoal,
        SUM(total_electricity) treeNum,
        NOW() statisticalTime
        FROM bto_plant_base
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
            <if test="query.createStartTime!=null and query.createEndTime!=null and query.createStartTime!='' and query.createEndTime!=''">
                AND (create_time
                BETWEEN #{query.createStartTime} AND date_add(#{query.createEndTime},interval 1 day))
            </if>
        </where>
    </select>
    <select id="getDeviceNumInfo" resultType="java.util.HashMap">
        SELECT COUNT(*) statusNum,
        `status` plantStatus
        FROM bto_photovoltaic.bto_device t1
        LEFT JOIN bto_plant_base t2 ON t1.plant_uid = t2.plant_uid
        <where>
            t1.is_deleted = 0 AND t2.is_deleted = 0
            <if test="query.createStartTime!=null and query.createEndTime!=null and query.createStartTime!='' and query.createEndTime!=''">
                AND (t2.create_time BETWEEN #{query.createStartTime} AND #{query.createEndTime})
            </if>
            AND t2.project_special IN
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
            GROUP BY plantStatus
        </where>
    </select>
    <select id="getPeriodElectricity" resultType="java.lang.String">
        SELECT sum(electricity)
        FROM bto_plant_day t1
        <where>
            <if test="query.dataStartTime!=null and query.dataEndTime!=null and query.dataStartTime!='' and query.dataEndTime!=''">
                <choose>
                    <!-- 如果传递的是月份，例如 '2024-02' -->
                    <when test="query.dataStartTime.length() == 7 and query.dataEndTime.length() == 7">
                        LEFT(t1.collect,7) BETWEEN #{query.dataStartTime} AND #{query.dataEndTime}
                    </when>
                    <!-- 如果传递的是天，例如 '2024-02-29' -->
                    <otherwise>
                        t1.collect BETWEEN #{query.dataStartTime} AND #{query.dataEndTime}
                    </otherwise>
                </choose>
            </if>
            AND
            EXISTS (
            SELECT plant_uid FROM bto_plant_base
            <where>
                <if test="query.createStartTime!=null and query.createEndTime!=null and query.createStartTime!='' and query.createEndTime!=''">
                    AND (date(create_time) BETWEEN #{query.createStartTime} AND #{query.createEndTime})
                </if>
                AND project_special IN
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
                AND plant_uid = t1.plant_uid
            </where>
            )
        </where>
    </select>
    <select id="projectInspectionStatistics" resultType="com.bto.commons.pojo.vo.ProjectInspectionInfoVO">
        SELECT
        LEFT(bpb.project_special,1) plantType,
        bpb.city,
        bpb.project_special projectId,
        COUNT(DISTINCT bpb.plant_uid) plantNum,
        SUM(bpb.plant_capacity) plantCapacity,
        SUM(bpd.electricity) electricity,
        SUM(CASE WHEN bpb.plant_status = 2 THEN 1 ELSE 0 END) alarmPlantNum
        FROM bto_plant_base bpb LEFT JOIN ${tableName} bpd ON bpb.plant_uid = bpd.plant_uid AND bpb.is_deleted = 0
        <where>
            <include refid="userInfoWithBpb"></include>
            <if test="startDate.length() != 4 and endDate.length() != 4">
                AND  bpd.collect between #{startDate} and #{endDate}
            </if>
            <if test="startDate.length() == 4 and endDate.length() == 4">
                AND  LEFT(bpd.collect,4) between #{startDate} and #{endDate}
            </if>
            <if test="projectSpecial != null and projectSpecial != '' ">
                AND bpb.project_special LIKE CONCAT(#{projectSpecial},'%')
            </if>
        </where>

        GROUP BY bpb.project_special,city
        ORDER BY plantType DESC
    </select>
    <select id="getMonthElectricity" resultType="com.bto.commons.pojo.vo.PlantMonthStatementVO">
        WITH w_plant AS (
        SELECT
        plant_uid,
        plant_name,
        plant_capacity,
        project_special,
        CASE
        WHEN project_special LIKE '1%' THEN '户用'
        WHEN project_special LIKE '2%' THEN '整县'
        WHEN project_special LIKE '3%' THEN '工商业'
        WHEN project_special LIKE '4%' THEN '户租'
        WHEN project_special LIKE '5%' THEN '产品服务'
        ELSE '未知'
        END AS projectType,
        inverter_num,
        total_electricity,
        month_electricity,
        address,
        CASE WHEN FLOOR(plant_capacity / 550) = plant_capacity / 550 THEN plant_capacity / 550 ELSE NULL END AS
        capacity550,
        CASE WHEN FLOOR(plant_capacity / 545) = plant_capacity / 545 THEN plant_capacity / 545 ELSE NULL END AS
        capacity545,
        create_time
        FROM
        bto_plant_base
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
            /*AND project_special LIKE '200%'*/
            AND date( create_time ) &lt; ( CONCAT( #{query.monthly}, '-01' ) + INTERVAL 1 MONTH )
        </where>
        ),
        w_electricity AS (
        SELECT
        plant_uid,
        LEFT ( collect, 7 ) collect_month,
        MAX( CASE DAY ( collect ) WHEN 1 THEN electricity / 100 ELSE '' END ) day1,
        MAX( CASE DAY ( collect ) WHEN 2 THEN electricity / 100 ELSE '' END ) day2,
        MAX( CASE DAY ( collect ) WHEN 3 THEN electricity / 100 ELSE '' END ) day3,
        MAX( CASE DAY ( collect ) WHEN 4 THEN electricity / 100 ELSE '' END ) day4,
        MAX( CASE DAY ( collect ) WHEN 5 THEN electricity / 100 ELSE '' END ) day5,
        MAX( CASE DAY ( collect ) WHEN 6 THEN electricity / 100 ELSE '' END ) day6,
        MAX( CASE DAY ( collect ) WHEN 7 THEN electricity / 100 ELSE '' END ) day7,
        MAX( CASE DAY ( collect ) WHEN 8 THEN electricity / 100 ELSE '' END ) day8,
        MAX( CASE DAY ( collect ) WHEN 9 THEN electricity / 100 ELSE '' END ) day9,
        MAX( CASE DAY ( collect ) WHEN 10 THEN electricity / 100 ELSE '' END ) day10,
        MAX( CASE DAY ( collect ) WHEN 11 THEN electricity / 100 ELSE '' END ) day11,
        MAX( CASE DAY ( collect ) WHEN 12 THEN electricity / 100 ELSE '' END ) day12,
        MAX( CASE DAY ( collect ) WHEN 13 THEN electricity / 100 ELSE '' END ) day13,
        MAX( CASE DAY ( collect ) WHEN 14 THEN electricity / 100 ELSE '' END ) day14,
        MAX( CASE DAY ( collect ) WHEN 15 THEN electricity / 100 ELSE '' END ) day15,
        MAX( CASE DAY ( collect ) WHEN 16 THEN electricity / 100 ELSE '' END ) day16,
        MAX( CASE DAY ( collect ) WHEN 17 THEN electricity / 100 ELSE '' END ) day17,
        MAX( CASE DAY ( collect ) WHEN 18 THEN electricity / 100 ELSE '' END ) day18,
        MAX( CASE DAY ( collect ) WHEN 19 THEN electricity / 100 ELSE '' END ) day19,
        MAX( CASE DAY ( collect ) WHEN 20 THEN electricity / 100 ELSE '' END ) day20,
        MAX( CASE DAY ( collect ) WHEN 21 THEN electricity / 100 ELSE '' END ) day21,
        MAX( CASE DAY ( collect ) WHEN 22 THEN electricity / 100 ELSE '' END ) day22,
        MAX( CASE DAY ( collect ) WHEN 23 THEN electricity / 100 ELSE '' END ) day23,
        MAX( CASE DAY ( collect ) WHEN 24 THEN electricity / 100 ELSE '' END ) day24,
        MAX( CASE DAY ( collect ) WHEN 25 THEN electricity / 100 ELSE '' END ) day25,
        MAX( CASE DAY ( collect ) WHEN 26 THEN electricity / 100 ELSE '' END ) day26,
        MAX( CASE DAY ( collect ) WHEN 27 THEN electricity / 100 ELSE '' END ) day27,
        MAX( CASE DAY ( collect ) WHEN 28 THEN electricity / 100 ELSE '' END ) day28,
        MAX( CASE DAY ( collect ) WHEN 29 THEN electricity / 100 ELSE '' END ) day29,
        MAX( CASE DAY ( collect ) WHEN 30 THEN electricity / 100 ELSE '' END ) day30,
        MAX( CASE DAY ( collect ) WHEN 31 THEN electricity / 100 ELSE '' END ) day31
        FROM
        bto_plant_day t1
        WHERE
        collect LIKE CONCAT( #{query.monthly}, '%' )
        AND EXISTS ( SELECT 1 FROM w_plant WHERE plant_uid = t1.plant_uid )
        GROUP BY
        plant_uid
        ) SELECT
        w1.plant_uid,
        w1.plant_name,
        w1.plant_capacity,
        w1.inverter_num,
        w1.total_electricity,
        w1.month_electricity,
        w1.address,
        w1.create_time,
        w1.projectType,
        w1.capacity550,
        w1.capacity545,
        w2.*
        FROM
        w_plant w1
        LEFT JOIN w_electricity w2 ON w1.plant_uid = w2.plant_uid
        ORDER BY
        w1.create_time DESC;
    </select>
    <select id="getMonthAlarm" resultType="com.bto.commons.pojo.vo.PlantMonthStatementVO">
        WITH w_plant AS (
        SELECT
        plant_uid,
        inverter_sn
        FROM
        bto_inverter_latest
        <where>
            is_deleted = 0
            /*AND project_special LIKE '200%'*/
            <include refid="userInfo"/> <include refid="userInfo"/>
            AND date( create_time ) &lt; ( CONCAT( #{query.monthly}, '-01' ) + INTERVAL 1 MONTH )
        </where>
        ),
        w_alarm_info AS (
        SELECT
        plant_uid,
        start_time,
        end_time,
        alarm_msg
        FROM
        (
        SELECT
        t1.plant_uid,
        1 type,
        t2.start_time,
        t2.end_time,
        t2.alarm_mean alarm_msg
        FROM
        w_plant t1
        LEFT JOIN v_inverter_alarm t2 ON t1.inverter_sn = t2.inverter_sn
        WHERE
        t2.start_time LIKE CONCAT( #{query.monthly}, '%' ) UNION ALL
        SELECT
        plant_uid,
        2 type,
        start_time,
        end_time,
        alarm_msg
        FROM
        bto_edge_server_alarm t1
        WHERE
        EXISTS ( SELECT 1 FROM w_plant WHERE plant_uid = t1.plant_uid )
        AND start_time LIKE CONCAT( #{query.monthly}, '%' )
        ) t3
        GROUP BY
        plant_uid
        ) SELECT
        plant_uid,
        MAX( CASE DAY ( start_time ) WHEN 1 THEN CONCAT( alarm_msg ) ELSE '' END ) day1Status,
        MAX( CASE DAY ( start_time ) WHEN 2 THEN CONCAT( alarm_msg ) ELSE '' END ) day2Status,
        MAX( CASE DAY ( start_time ) WHEN 3 THEN CONCAT( alarm_msg ) ELSE '' END ) day3Status,
        MAX( CASE DAY ( start_time ) WHEN 4 THEN CONCAT( alarm_msg ) ELSE '' END ) day4Status,
        MAX( CASE DAY ( start_time ) WHEN 5 THEN CONCAT( alarm_msg ) ELSE '' END ) day5Status,
        MAX( CASE DAY ( start_time ) WHEN 6 THEN CONCAT( alarm_msg ) ELSE '' END ) day6Status,
        MAX( CASE DAY ( start_time ) WHEN 7 THEN CONCAT( alarm_msg ) ELSE '' END ) day7Status,
        MAX( CASE DAY ( start_time ) WHEN 8 THEN CONCAT( alarm_msg ) ELSE '' END ) day8Status,
        MAX( CASE DAY ( start_time ) WHEN 9 THEN CONCAT( alarm_msg ) ELSE '' END ) day9Status,
        MAX( CASE DAY ( start_time ) WHEN 10 THEN CONCAT( alarm_msg ) ELSE '' END ) day10Status,
        MAX( CASE DAY ( start_time ) WHEN 11 THEN CONCAT( alarm_msg ) ELSE '' END ) day11Status,
        MAX( CASE DAY ( start_time ) WHEN 12 THEN CONCAT( alarm_msg ) ELSE '' END ) day12Status,
        MAX( CASE DAY ( start_time ) WHEN 13 THEN CONCAT( alarm_msg ) ELSE '' END ) day13Status,
        MAX( CASE DAY ( start_time ) WHEN 14 THEN CONCAT( alarm_msg ) ELSE '' END ) day14Status,
        MAX( CASE DAY ( start_time ) WHEN 15 THEN CONCAT( alarm_msg ) ELSE '' END ) day15Status,
        MAX( CASE DAY ( start_time ) WHEN 16 THEN CONCAT( alarm_msg ) ELSE '' END ) day16Status,
        MAX( CASE DAY ( start_time ) WHEN 17 THEN CONCAT( alarm_msg ) ELSE '' END ) day17Status,
        MAX( CASE DAY ( start_time ) WHEN 18 THEN CONCAT( alarm_msg ) ELSE '' END ) day18Status,
        MAX( CASE DAY ( start_time ) WHEN 19 THEN CONCAT( alarm_msg ) ELSE '' END ) day19Status,
        MAX( CASE DAY ( start_time ) WHEN 20 THEN CONCAT( alarm_msg ) ELSE '' END ) day20Status,
        MAX( CASE DAY ( start_time ) WHEN 21 THEN CONCAT( alarm_msg ) ELSE '' END ) day21Status,
        MAX( CASE DAY ( start_time ) WHEN 22 THEN CONCAT( alarm_msg ) ELSE '' END ) day22Status,
        MAX( CASE DAY ( start_time ) WHEN 23 THEN CONCAT( alarm_msg ) ELSE '' END ) day23Status,
        MAX( CASE DAY ( start_time ) WHEN 24 THEN CONCAT( alarm_msg ) ELSE '' END ) day24Status,
        MAX( CASE DAY ( start_time ) WHEN 25 THEN CONCAT( alarm_msg ) ELSE '' END ) day25Status,
        MAX( CASE DAY ( start_time ) WHEN 26 THEN CONCAT( alarm_msg ) ELSE '' END ) day26Status,
        MAX( CASE DAY ( start_time ) WHEN 27 THEN CONCAT( alarm_msg ) ELSE '' END ) day27Status,
        MAX( CASE DAY ( start_time ) WHEN 28 THEN CONCAT( alarm_msg ) ELSE '' END ) day28Status,
        MAX( CASE DAY ( start_time ) WHEN 29 THEN CONCAT( alarm_msg ) ELSE '' END ) day29Status,
        MAX( CASE DAY ( start_time ) WHEN 30 THEN CONCAT( alarm_msg ) ELSE '' END ) day30Status,
        MAX( CASE DAY ( start_time ) WHEN 31 THEN CONCAT( alarm_msg ) ELSE '' END ) day31Status
        FROM
        w_alarm_info
        GROUP BY
        plant_uid
    </select>
    <select id="collect" resultType="com.bto.statistics.pojo.MonthData">
        WITH aggregated_predict AS (SELECT plant_uid,
        SUM(predict_electricity) AS total_predict_electricity,
        SUM(electricity) AS total_electricity
        FROM v_predict_electricity
        WHERE collect_date BETWEEN #{start} AND #{end}
        GROUP BY plant_uid)
        SELECT bpb.city,
        COUNT(bpb.plant_uid) AS totalPlant,
        SUM(bpb.plant_capacity) AS totalCapacity,
        SUM(ap.total_predict_electricity) AS preElectricity,
        SUM(ap.total_electricity) AS realElectricity
        FROM bto_plant_base bpb
        LEFT JOIN aggregated_predict ap ON bpb.plant_uid = ap.plant_uid
        <where>
            <include refid="userInfoWithBpb"/>
        </where>
        GROUP BY bpb.city
        ORDER BY preElectricity DESC
    </select>
    <select id="all" resultType="com.bto.statistics.pojo.AllData">
        WITH aggregated_predict AS (SELECT plant_uid,
        SUM(predict_electricity) AS total_predict_electricity,
        SUM(electricity) AS total_electricity
        FROM v_predict_electricity
        WHERE collect_date BETWEEN #{start} AND #{end}
        GROUP BY plant_uid)
        SELECT bpb.plant_uid,
        bpb.plant_name,
        bpb.city,
        bpb.area,
        bpb.town,
        v.user_name,
        bpb.create_time,
        bpb.plant_capacity,
        SUM(ap.total_predict_electricity) AS preElectricity,
        SUM(ap.total_electricity) AS realElectricity
        FROM bto_plant_base bpb
        LEFT JOIN aggregated_predict ap ON bpb.plant_uid = ap.plant_uid
        LEFT JOIN v_user_plant v ON bpb.plant_uid = v.plant_uid
        <where>
            <include refid="userInfoWithBpb"/>
        </where>
        GROUP BY bpb.plant_uid
        ORDER BY preElectricity DESC
    </select>


</mapper>