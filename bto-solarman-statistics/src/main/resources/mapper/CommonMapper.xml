<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.statistics.dao.CommonMapper">
    <select id="checkTableExistsWithSchema"
            resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM information_schema.tables
        WHERE
            table_schema = #{tableSchema}
          AND table_name = #{tableName}
    </select>
    <select id="checkTableExistsWithShow" resultType="java.util.HashMap">
        SHOW TABLES LIKE #{tableName}
    </select>
</mapper>