package com.bto.gatewayx.config;

import com.bto.gatewayx.interceptor.GatewayInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 *  CustomWebMvcConfigurer 自定义拦截器后，需要配置进Spring
 * 也可以mapping，跨域设置
 * <AUTHOR>
 *
 * @date 2023/07/17
 */
@Configuration
public class CustomWebMvcConfigurer implements WebMvcConfigurer {

    /**
     * 表明这个类是一个配置类，继承WebMvcConfigurer
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //这里用来排除拦截的接口,例如登录前调用的接口
        List<String> excludePath = new ArrayList<>();
        // 注册拦截器
        registry.addInterceptor(new GatewayInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(excludePath);
        WebMvcConfigurer.super.addInterceptors(registry);
    }
}


