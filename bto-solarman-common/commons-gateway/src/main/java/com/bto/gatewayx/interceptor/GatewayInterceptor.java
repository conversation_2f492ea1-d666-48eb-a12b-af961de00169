package com.bto.gatewayx.interceptor;

import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.gatewayx.constatnts.SystemConstant;
import com.bto.redis.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 网关拦截器
 * <AUTHOR>
 * @date 2023/7/12 15:37
 */
@Configuration
public class GatewayInterceptor implements HandlerInterceptor {
    /**
     * 注入redis服务
     */
    private static RedisUtil redisUtil;
    @Autowired
    public void setRedisService(RedisUtil redisUtil){
        GatewayInterceptor.redisUtil = redisUtil;
    }

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws IOException {
        String header = request.getHeader(SystemConstant.REQUEST_TYPE);
        if (header != null && header.equals(SystemConstant.REQUEST_TYPE_FEIGN)){
            return true;
        }
        String gatewayToken = request.getHeader(SystemConstant.GATEWAY_HEADER);
        String redisToken = null;
        Object obj = redisUtil.get(SystemConstant.GATEWAY_TOKEN);
        if (obj != null){
            redisToken = obj.toString();
        }
        if(gatewayToken==null || !gatewayToken.equals(redisToken)) {
            response.setContentType("application/json;charset=utf-8");
            Result<String> result = new Result<>();
            result.setStatus(ResultEnum.ACCESS_REFUSED_GATEWAY.getCode());
            result.setMessage(ResultEnum.ACCESS_REFUSED_GATEWAY.getMessage());
            throw  new BusinessException(ResultEnum.ACCESS_REFUSED_GATEWAY);
        }
        return true;
    }

}
