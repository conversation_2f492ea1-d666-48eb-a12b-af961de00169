package com.bto.api.feign.devicemanage;

import com.bto.commons.pojo.dto.AddDeviceDTO;
import com.bto.commons.pojo.dto.IotCardQuery;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.BtoElectricityMeter;
import com.bto.commons.pojo.entity.InverterComponent;
import com.bto.commons.pojo.vo.PlantPowerVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 16:10
 */
@Component
public class DeviceServiceClientFallbackFactory implements FallbackFactory<DeviceServiceClient> {
    @Override
    public DeviceServiceClient create(Throwable e) {
        return new DeviceServiceClient() {
            @Override
            public Result initial(String imei) {
                return null;
            }

            @Override
            public Result addDevice(AddDeviceDTO addDeviceDTO) {
                return null;
            }

            @Override
            public Result addInverterComponent(List<InverterComponent> list) {
                return null;
            }

            @Override
            public Result<List<BtoElectricityMeter>> getInfoById(String plantUid) {
                e.printStackTrace();
                return null;
            }

            @Override
            public Result getDevicesId(String plantUid) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getOnlineInverterStats(String plantUid) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getInverterAlarmLatestStatsByPlantUid(String plantUid, String status) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getInverterAlarmLatestStatsByPlantUids(List<String> plantUids) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getTodayPeakPowerByPlantUid(String plantUid) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result deleteDeviceByPlantUid(String plantUid) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result deviceIsExisted(String deviceId, String isDeleted) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getInverterSnList(String plantUid) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getPlantMaxPower(String date, List<String> inverterSnList) {
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode(),
                        ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getMessage(),
                        e.getMessage());
            }

            @Override
            public Result getIotCardByPage(IotCardQuery query, String authorization) {
                e.printStackTrace();
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD, e.getCause().getMessage());
            }

            @Override
            public Result<List<SmsInfoDTO>> getSmsInfo(List<String> cimiList) {
                e.printStackTrace();
                return Result.instance(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD, null);
            }

            @Override
            public List<PlantPowerVO> getTotalPowerListByPlantUid(String plantId, String date) {
                e.printStackTrace();
                return null;
            }

            @Override
            public List<String> getCimiListByPhone(List<String> phoneNumbers) {
                e.printStackTrace();
                return null;
            }

            @Override
            public Result<List<String>> getListByType(String plantUid, String type) {
                e.printStackTrace();
                return null;
            }
        };
    }
}
