package com.bto.gateway.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import springfox.documentation.swagger.web.*;

import java.util.Optional;

/**
 * Swagger处理器
 * 提供Swagger UI的配置和文档资源接口，支持安全配置和UI配置的获取
 * 为网关提供统一的Swagger文档访问入口
 *
 * <AUTHOR>
 * @date 2023/4/10 11:25
 */
@RestController
public class SwaggerHandler {

    @Autowired(required = false)
    private SecurityConfiguration securityConfiguration;

    @Autowired(required = false)
    private UiConfiguration uiConfiguration;

    private final SwaggerResourcesProvider swaggerResources;

    /**
     * 构造函数
     * 注入Swagger资源提供者，用于获取API文档资源
     * @param swaggerResources Swagger资源提供者实例
     */
    @Autowired
    public SwaggerHandler(SwaggerResourcesProvider swaggerResources) {
        this.swaggerResources = swaggerResources;
    }


    /**
     * 获取Swagger安全配置
     * 提供Swagger UI的安全配置信息，支持API访问的安全控制
     * @return Mono<ResponseEntity<SecurityConfiguration>> 安全配置响应对象
     */
    @GetMapping("/swagger-resources/configuration/security")
    public Mono<ResponseEntity<SecurityConfiguration>> securityConfiguration() {
        return Mono.just(new ResponseEntity<>(
                Optional.ofNullable(securityConfiguration).orElse(SecurityConfigurationBuilder.builder().build()), HttpStatus.OK));
    }

    /**
     * 获取Swagger UI配置
     * 提供Swagger UI的界面配置信息，包括显示设置和交互配置
     * @return Mono<ResponseEntity<UiConfiguration>> UI配置响应对象
     */
    @GetMapping("/swagger-resources/configuration/ui")
    public Mono<ResponseEntity<UiConfiguration>> uiConfiguration() {
        return Mono.just(new ResponseEntity<>(
                Optional.ofNullable(uiConfiguration).orElse(UiConfigurationBuilder.builder().build()), HttpStatus.OK));
    }

    /**
     * 获取Swagger资源列表
     * 提供所有可用的API文档资源列表，用于Swagger UI的文档展示
     * @return Mono<ResponseEntity> 资源列表响应对象
     */
    @GetMapping("/swagger-resources")
    public Mono<ResponseEntity> swaggerResources() {
        return Mono.just((new ResponseEntity<>(swaggerResources.get(), HttpStatus.OK)));
    }
}
