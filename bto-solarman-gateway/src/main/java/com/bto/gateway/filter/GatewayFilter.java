package com.bto.gateway.filter;

import cn.hutool.core.lang.UUID;
import com.bto.gatewayx.constatnts.SystemConstant;
import com.bto.redis.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 全局网关过滤器
 * 负责为所有经过网关的请求生成并维护网关令牌，实现请求的统一认证和追踪
 * 令牌存储在Redis中，默认有效期为5分钟，支持令牌的自动续期
 *
 * <AUTHOR>
 * @date 2023-07-12
 */
@Configuration
public class GatewayFilter implements GlobalFilter {
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 过滤器核心方法
     * 处理所有经过网关的请求，生成或更新网关令牌，并将令牌添加到请求头中
     * @param exchange 服务器Web交换对象，包含请求和响应信息
     * @param chain 网关过滤器链，用于继续处理请求
     * @return Mono<Void> 响应式处理结果
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        Object obj = redisUtil.get("gateway_token");
        String gatewayToken = null;
        if (obj!=null){
            gatewayToken = obj.toString();
        }
        // 将gatewayToken保存至redis
        if (gatewayToken==null){
            //生成gatewayToken
            gatewayToken = UUID.randomUUID().toString();
            redisUtil.set(SystemConstant.GATEWAY_TOKEN, gatewayToken,300L);
        }else {
            redisUtil.set(SystemConstant.GATEWAY_TOKEN, gatewayToken,300L);
        }
        // 写入请求头
        ServerHttpRequest serverHttpRequest = exchange.getRequest().mutate().header(SystemConstant.GATEWAY_HEADER, gatewayToken).build();
        ServerWebExchange newExchange = exchange.mutate().request(serverHttpRequest).build();
        return chain.filter(newExchange);
    }
/**
 *  Column 'ENCRYPTED_DATA_KEY' is either not in any table in the FROM list or appears within a join specification and is outside the scope of the join specification or appears in a HAVING clause and is not in the GROUP BY list. If this is a CREATE or ALTER TABLE statement then 'ENCRYPTED_DATA_KEY' is not a column in the target table.;
 *  caused: Column 'ENCRYPTED_DATA_KEY' is either not in any table in the FROM list or appears within a join specification and is outside the scope of the join specification or appears in a HAVING clause and is not in the GROUP BY list. If this is a CREATE or ALTER TABLE statement then 'ENCRYPTED_DATA_KEY' is not a column in the target table.;caused: Column 'ENCRYPTED_DATA_KEY' is either not in any table in the FROM list or appears within a join specification and is outside the scope of the join specification or appears in a HAVING clause and is not in the GROUP BY list.
 *  If this is a CREATE or ALTER TABLE statement then 'ENCRYPTED_DATA_KEY' is not a column in the target table.;
 */
}