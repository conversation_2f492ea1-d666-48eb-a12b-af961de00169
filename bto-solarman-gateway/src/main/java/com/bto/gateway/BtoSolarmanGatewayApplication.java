package com.bto.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 网关服务启动类
 * 作为bto-solarman-gateway模块的入口点，负责启动Spring Cloud Gateway网关服务
 * 支持服务发现和路由转发功能，为整个光伏系统提供统一的API网关入口
 *
 * <AUTHOR>
 * @date 2023/3/29 23:35
 */
@SpringBootApplication(scanBasePackages = {"com.bto.redis.*","com.bto.gateway.*"})
@EnableDiscoveryClient
public class BtoSolarmanGatewayApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(BtoSolarmanGatewayApplication.class);
        springApplication.run(args);
    }

}
