spring:
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
    timeout: 5000
    password: botongredis666
  cloud:
    nacos:
      discovery:
        namespace: light-cloud
      config:
        namespace: light-cloud
logging:
  config: classpath:log4j2-prod.xml
knife4j:
  enable-aggregation: true
  nacos:
    enable: true
    service-url: http://127.0.0.1:8848/nacos/
    routes:
      - name: solarman-security
        service-name: solarman-system
        namespace-id: light-cloud
        clusters: dev
        location: /system?group=default
      - name: solarman-security
        service-name: solarman-security
        namespace-id: light-cloud
        clusters: dev
        location: /security?group=default
      - name: solarman-device
        service-name: solarman-device
        namespace-id: light-cloud
        clusters: dev
        location: /device?group=default
      - name: solarman-plant
        service-name: solarman-plant
        namespace-id: light-cloud
        clusters: dev
        location: /plant?group=default
      - name: solarman-alarm
        service-name: solarman-alarm
        namespace-id: light-cloud
        clusters: dev
        location: /alarm?group=default
      - name: solarman-statistics
        service-name: solarman-statistics
        namespace-id: light-cloud
        clusters: dev
        location: /statistics?group=default
      - name: solarman-engineer
        service-name: solarman-engineer-app
        namespace-id: light-cloud
        clusters: dev
        location: /engineer?group=default