spring:
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
    timeout: 5000
  cloud:
    nacos:
      discovery:
        namespace: d7b28a7f-9929-4129-a774-202be7f0bd61
      config:
        namespace: d7b28a7f-9929-4129-a774-202be7f0bd61
logging:
  config: classpath:log4j2-dev.xml
knife4j:
  enable-aggregation: true
  nacos:
    enable: true
    service-url: http://127.0.0.1:8848/nacos/
    routes:
      - name: solarman-security
        service-name: solarman-system
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /system?group=default
      - name: solarman-security
        service-name: solarman-security
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /security?group=default
      - name: solarman-device
        service-name: solarman-device
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /device?group=default
      - name: solarman-plant
        service-name: solarman-plant
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /plant?group=default
      - name: solarman-alarm
        service-name: solarman-alarm
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /alarm?group=default
      - name: solarman-statistics
        service-name: solarman-statistics
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /statistics?group=default
      - name: solarman-engineer
        service-name: solarman-engineer
        namespace-id: d7b28a7f-9929-4129-a774-202be7f0bd61
        clusters: dev
        location: /engineer?group=default