server:
  port: 41000
spring:
  profiles:
    active: dev
  application:
    name: solarman-gateway
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        service: solarman-gateway
      config:
        server-addr: 127.0.0.1:8848
        prefix: solarman-gateway
        file-extension: yaml
        refresh-enabled: false
    gateway:
      globalcors:
        add-to-simple-url-handler-mapping: true
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods:
              - "GET"
              - "POST"
              - "DELETE"
              - "PUT"
              - "OPTIONS"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 360000
      routes:
        - id: 系统模块服务
          uri: http://localhost:41100
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        - id: 系统监控服务
          uri: http://localhost:40000
          predicates:
            - Path=/monitor/**
          filters:
            - StripPrefix=1
        - id: 电站模块服务
          uri: http://localhost:41200
          predicates:
            - Path=/plant/**
          filters:
            - StripPrefix=1
        - id: 设备模块服务
          uri: http://localhost:41300
          predicates:
            - Path=/device/**
          filters:
            - StripPrefix=1
        - id: 告警模块服务
          uri: http://localhost:41400
          predicates:
            - Path=/alarm/**
          filters:
            - StripPrefix=1
        - id: 统计模块服务
          uri: http://localhost:41500
          predicates:
            - Path=/statistics/**
          filters:
            - StripPrefix=1
        - id: 工程师app模块
          uri: http://localhost:41600
          predicates:
            - Path=/engineer/**
          filters:
            - StripPrefix=1