import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bto.commons.constant.NoticeStateEnum;
import com.bto.commons.constant.SystemEnum;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.utils.DateUtils;
import com.bto.system.BtoSolarmanSystemApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/30 16:57
 */
@SpringBootTest(classes = BtoSolarmanSystemApplication.class)
@RunWith(SpringRunner.class)
public class TestLab {
    // @Test
    // public void testJwt() {
    //     String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2luZm8iOnsidXNlcm5hbWUiOiLmtYvor5UiLCJ1c2VyVUlEIjoiQzlFRUQ0MkEtQjZGMC00RTAzLThDNUMtMjI4NUVDNUI0NURDIiwidXNlclR5cGUiOiIwIiwicm9sZUlEIjoiMjMxNDAwMDAwNCIsInByb2plY3RJRCI6IjEwMyIsIm1lbnVMaXN0IjpudWxsfSwidXNlcl9uYW1lIjoi5rWL6K-VIiwic2NvcGUiOlsiYWxsIl0sImV4cCI6MTcyMTQzNjEzNSwiYXV0aG9yaXRpZXMiOlsidXNlcjphZGQiXSwianRpIjoiZjBiOWUxYmMtNDJjZi00YzY5LWIzNzUtOTY5YWIzZjdkMmYzIiwiY2xpZW50X2lkIjoiYWRtaW4ifQ.iMuUIoB0TgAA7-IaxNW_NKPqEEMG1bUoUHo-URrrt5c";
    //     // 创建解析对象，使用的算法和secret要与创建token时保持一致
    //     JWTVerifier jwtVerifier = JWT.require((Algorithm.HMAC256(SystemEnum.SIGN_KEY.getName()))).build();
    //     // 解析指定的token
    //     DecodedJWT decodedJWT = jwtVerifier.verify(jwt);
    //     System.out.println(decodedJWT);
    //     Claim claim = decodedJWT.getClaim("user_info");
    //     Map<String, Object> userInfo = claim.asMap();
    //     userInfo.forEach((key, value) -> System.out.println(key + "=" + value));
    //     RequireParamsDTO requireParams = new RequireParamsDTO();
    //     for (String key : userInfo.keySet()) {
    //         if (key.equals("userUid")) {
    //             requireParams.setUserUid(userInfo.get(key).toString());
    //         } else if (key.equals("userType")) {
    //             requireParams.setUserType(userInfo.get(key).toString());
    //         } else if (key.equals("roleID")) {
    //             requireParams.setRoleID(userInfo.get(key).toString());
    //         } else if (key.equals("projectID")) {
    //             requireParams.setProjectID(Collections.singletonList(userInfo.get(key).toString()));
    //         }
    //     }
    //     System.out.println(requireParams.toString());
    // }

    @Test
    public void testUUID() {
        long time = DateUtils.getExpTimeByEndOfToday();
        System.out.println(time);


    }

    @Test
    public void testResource() {
        ResourceParameter parameter = new ResourceParameter();
        // 定义权重系数
        double sunshineWeight = 0.3; // 50% 的权重
        double radiationWeight = 0.3; // 30% 的权重
        double tempWeight = 0.2; // 20% 的权重
        double otherWeight = 0.2; // 20% 的权重（其他因素）

        // // 计算差值
        // double temperatureDiff = Math.abs(guangzhouData.getTemperature() - averageData.getTemperature());
        // double weatherDiff = Math.abs(guangzhouData.getWeatherFactor() - averageData.getWeatherFactor());
        // double otherFactorsDiff = calculateOtherFactorsDiff(guangzhouData, averageData);

        // 应用权重
        double sunshine = sunshineWeight * parameter.getSunshineDuration().doubleValue();
        double radiation = radiationWeight * parameter.getRadiantQuantityTotal().doubleValue();
        double temp = tempWeight * parameter.getTempAvg().doubleValue();
        double other = otherWeight * parameter.getPrecipitation().doubleValue() * -1;

        // 计算加权差值
        double evaluationScore = (sunshine + radiation + temp + other) / 10.0;

        // 保留两位小数
        evaluationScore = BigDecimal.valueOf(evaluationScore).setScale(2, RoundingMode.HALF_UP).doubleValue();

        // 输出评估分数
        System.out.println("广州市资源：" + parameter + "\n资源评估分数：" + evaluationScore);
    }


    @Test

    public void test() {

    }
}
