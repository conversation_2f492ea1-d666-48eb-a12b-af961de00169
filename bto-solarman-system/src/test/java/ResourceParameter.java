import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/1/5 8:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceParameter {
    private String province = "广东省";
    private String city = "韶关市";
    private String area = "始兴县";
    private String collectYear = "2023";
    //年日照时间
    private BigDecimal sunshineDuration = BigDecimal.valueOf(4401.90);
    //年降雨量
    private BigDecimal precipitation = BigDecimal.valueOf(1825.00);
    //平均温度
    private BigDecimal tempAvg = BigDecimal.valueOf(19.60);
    //年辐射量
    private BigDecimal radiantQuantityTotal = BigDecimal.valueOf(102.16);

}
