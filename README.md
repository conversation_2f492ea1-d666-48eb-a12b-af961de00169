# 博通新能源光伏运维平台 3.0

<div align="center">

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Java](https://img.shields.io/badge/Java-8-orange.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.3.5-brightgreen.svg)
![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-Hoxton.SR9-blue.svg)
![Spring Cloud Alibaba](https://img.shields.io/badge/Spring%20Cloud%20Alibaba-2.2.0-red.svg)

</div>

## 📖 项目简介

博通新能源光伏运维平台 3.0 是由广东博通新能源科技有限公司自主研发的新一代光伏电站智能运维管理系统。基于微服务架构设计，采用 Spring Cloud Alibaba 技术栈，为光伏电站提供全方位的实时监控、智能运维、数据分析和预测维护服务。

### 🎯 核心特性

- **🔄 微服务架构**：基于 Spring Cloud Alibaba 的分布式微服务架构
- **📊 实时监控**：光伏设备实时数据采集与监控
- **🚨 智能告警**：多维度告警机制，支持实时告警和历史告警查询
- **📈 数据分析**：发电量统计、效率分析、故障诊断等智能分析功能
- **🔐 权限管理**：基于 RBAC 的细粒度权限控制
- **📱 多端支持**：支持 Web 端和移动端（工程师 APP）
- **☁️ 云原生**：支持 Docker 容器化部署

### 🏗️ 系统架构

![系统架构图](./btoIMG/image-20230628102917210.png)

系统采用分层架构设计，包含以下核心组件：

#### 数据采集层
- **硬件设备**：逆变器、运维器、电表等光伏设备
- **通信协议**：ModBus、MQTT 等工业通信协议
- **数据采集**：实时采集设备运行数据

#### 数据传输层
- **MQTT 服务器**：负责设备数据的分发和传输
- **数据预处理**：对原始数据进行初步清洗和格式化

#### 数据处理层
- **数据清洗**：使用 Kafka、Flink 等大数据工具进行数据清洗
- **数据存储**：MySQL 数据库进行数据持久化存储
- **第三方数据**：支持第三方数据源的接入和同步

#### 应用服务层
- **微服务集群**：多个业务微服务提供不同功能模块
- **API 网关**：统一的服务入口和路由管理
- **服务治理**：服务注册发现、配置管理、熔断限流

#### 展示应用层
- **Web 管理端**：面向运维人员的 Web 管理界面
- **移动端 APP**：面向工程师的移动端应用
- **数据大屏**：实时数据展示大屏

![功能架构图](./btoIMG/image-20230628103301116.png)

## 🏢 微服务架构

### 服务模块

| 服务名称 | 端口 | 功能描述 |
|---------|------|----------|
| **bto-solarman-gateway** | 41000 | API 网关服务，负责路由转发、跨域处理、统一鉴权 |
| **bto-solarman-system** | 41100 | 系统管理服务，提供用户管理、权限管理、登录认证 |
| **bto-solarman-plant-manage** | 41200 | 电站管理服务，负责电站信息管理、发电预测 |
| **bto-solarman-device-manage** | 41300 | 设备管理服务，管理逆变器、运维器、电表等设备 |
| **bto-solarman-alarm-manage** | 41400 | 告警管理服务，处理设备告警、故障诊断 |
| **bto-solarman-statistics** | 41500 | 统计分析服务，提供发电量统计、报表生成 |
| **bto-engineer-app** | 41600 | 工程师移动端服务，支持移动端业务功能 |
| **bto-solarman-monitor** | 40000 | 系统监控服务，提供应用监控和健康检查 |

### 公共模块

| 模块名称 | 功能描述 |
|---------|----------|
| **commons-core** | 核心工具类、常量定义、异常处理 |
| **commons-api** | Feign 客户端、API 接口定义 |
| **commons-oauth** | OAuth2 认证授权 |
| **commons-redis** | Redis 缓存配置 |
| **commons-logs** | 日志配置和管理 |
| **commons-gateway** | 网关相关配置 |
| **commons-sentinel** | 熔断限流配置 |
| **commons-sms** | 短信服务 |

## 🛠️ 技术栈

### 核心框架

| 技术 | 版本 | 说明 |
|------|------|------|
| **Java** | 8 | 开发语言 |
| **Spring Boot** | 2.3.5.RELEASE | 基础框架 |
| **Spring Cloud** | Hoxton.SR9 | 微服务框架 |
| **Spring Cloud Alibaba** | 2.2.0.RELEASE | 阿里巴巴微服务套件 |

### 微服务组件

| 组件 | 版本 | 功能 |
|------|------|------|
| **Nacos** | 2.0.4 | 服务注册发现、配置中心 |
| **Spring Cloud Gateway** | 3.4.1 | API 网关 |
| **Sentinel** | 1.8.6 | 熔断限流 |
| **Seata** | 1.6.1 | 分布式事务 |
| **RocketMQ** | 4.9.4 | 消息队列 |

### 数据存储

| 技术 | 版本 | 功能 |
|------|------|------|
| **MySQL** | 5.7/8.0+ | 主数据库 |
| **Redis** | 5.0/6.0 | 缓存数据库 |
| **MyBatis Plus** | 3.5.3.1 | ORM 框架 |
| **Druid** | 1.2.16 | 数据库连接池 |
| **Dynamic Datasource** | 4.3.0 | 动态数据源 |

### 开发工具

| 工具 | 版本 | 功能 |
|------|------|------|
| **Knife4j** | 2.0.8 | API 文档 |
| **Hutool** | 5.8.18 | Java 工具库 |
| **MapStruct** | 1.5.5.Final | 对象映射 |
| **Lombok** | 1.18.26 | 代码简化 |

### 监控运维

| 工具 | 版本 | 功能 |
|------|------|------|
| **Spring Boot Admin** | 2.3.1 | 应用监控 |
| **XXL Job** | 2.3.1 | 定时任务 |
| **SkyWalking** | 8.12.0 | 链路追踪 |

### 云服务集成

| 服务 | 功能 |
|------|------|
| **阿里云 OSS** | 文件存储 |
| **阿里云短信** | 短信服务 |
| **腾讯云 COS** | 对象存储 |
| **七牛云** | 文件存储 |
| **MinIO** | 私有云存储 |
| **华为云 OBS** | 对象存储 |

## 🚀 功能模块

### 系统管理
- **用户管理**：用户信息维护、角色分配
- **角色管理**：角色权限配置、数据权限控制
- **菜单管理**：系统菜单配置、权限分配
- **项目管理**：项目信息管理、组织架构

### 电站管理
- **电站列表**：电站基础信息管理
- **电站详情**：实时数据监控、历史数据查询
- **发电预测**：基于气象数据的发电量预测
- **电站收藏**：个人电站收藏功能

### 设备管理
- **逆变器管理**：逆变器设备监控、参数配置
- **运维器管理**：运维器设备管理、通信监控
- **电表管理**：电表数据采集、用电统计
- **设备绑定**：设备添加、更换、解绑操作

### 告警管理
- **实时告警**：设备故障实时告警
- **历史告警**：告警历史记录查询
- **告警处理**：告警确认、处理流程
- **自检提示**：设备自检结果提醒

### 统计分析
- **发电量统计**：日、月、年发电量统计
- **效率分析**：发电效率、设备效率分析
- **收益统计**：发电收益计算、趋势分析
- **报表导出**：各类统计报表导出

### 数据分析
- **发电量运维**：发电量趋势分析、异常检测
- **故障诊断**：设备故障智能诊断
- **预测维护**：基于数据的预测性维护

### 工程师 APP
- **移动端管理**：支持工程师移动端操作
- **现场运维**：现场设备检修、数据采集
- **工单管理**：运维工单处理流程

## 📊 系统功能

![系统功能模块图](./btoIMG/image-20230628104433530.png)

### 核心功能特性

| 功能模块 | 功能描述 | 特性标记 |
|---------|----------|----------|
| **平台首页** | 实时数据展示、综合数据统计、三大屏页面 | ⭐️ |
| **电站管理** | 电站、逆变器、运维器等设备列表展示与统计 | ⭐️ |
| **设备监测** | 电站设备详细实时数据监控 | ⭐️ |
| **报警统计** | 实时报警、历史报警、自检提示监测查询 | ⭐️ |
| **统计报表** | 多维度数据统计分析、报表导出功能 | ⭐️ |
| **权限管理** | 角色菜单权限、数据范围权限划分 | 🚀 |
| **数据分析** | 发电效率分析、天气关联分析、运维指导 | 🚀 |
| **系统设置** | 系统配置管理、参数设置 | 🚀 |

> **图例说明**：
> - ⭐️ 表示重新实现的核心功能
> - 🚀 表示新增的增强功能

## 🐳 部署说明

### Docker 部署

每个微服务都提供了 Dockerfile，支持容器化部署：

```dockerfile
FROM java:8
VOLUME /tmp
COPY target/bto-solarman-gateway-0.0.1-SNAPSHOT.jar app.jar
RUN bash -c "touch /app.jar"
EXPOSE 41000
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./uramdom","-jar","/app.jar"]
```

### 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+ 或 8.0+
- **Redis**: 5.0+
- **Nacos**: 2.0.4+

### 启动顺序

1. 启动基础设施（MySQL、Redis、Nacos）
2. 启动网关服务（bto-solarman-gateway）
3. 启动系统服务（bto-solarman-system）
4. 启动业务服务（其他微服务）
5. 启动监控服务（bto-solarman-monitor）

## 📝 开发指南

### 项目结构

```
bto-solarman-parent/
├── bto-solarman-gateway/          # API 网关
├── bto-solarman-common/           # 公共模块
│   ├── commons-core/              # 核心工具
│   ├── commons-api/               # API 接口
│   ├── commons-oauth/             # 认证授权
│   ├── commons-redis/             # Redis 配置
│   ├── commons-logs/              # 日志配置
│   ├── commons-gateway/           # 网关配置
│   ├── commons-sentinel/          # 熔断限流
│   └── commons-sms/               # 短信服务
├── bto-solarman-system/           # 系统管理
├── bto-solarman-plant-manage/     # 电站管理
├── bto-solarman-device-manage/    # 设备管理
├── bto-solarman-alarm-manage/     # 告警管理
├── bto-solarman-statistics/       # 统计分析
├── bto-engineer-app/              # 工程师APP
├── bto-solarman-monitor/          # 系统监控
└── bto-solarman-module/           # 扩展模块
```

### 开发规范

- 遵循 RESTful API 设计规范
- 使用统一的返回结果封装
- 统一异常处理机制
- 完善的日志记录
- 单元测试覆盖

### API 文档

项目集成了 Knife4j，启动后可访问：
- 网关聚合文档：http://localhost:41000/doc.html
- 各服务独立文档：http://localhost:{port}/doc.html

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- **公司**：广东博通新能源科技有限公司
- **项目地址**：https://www.btosolarman.com/btosolar
- **技术支持**：请提交 Issue 或联系开发团队

## 📸 系统演示

### Web 管理端

#### 登录与首页
| 登录页面 | 系统首页 |
|---------|---------|
| ![登录页面](./btoIMG/image-20230628112952519.png) | ![系统首页](./btoIMG/image-20230628113127925.png) |

#### 数据大屏
| 综合大屏 | 站点轮播 |
|---------|---------|
| ![综合大屏](./btoIMG/image-20230628113159997.png) | ![站点轮播](./btoIMG/image-20230628114414944.png) |

#### 电站管理
| 电站列表 | 电站详情 |
|---------|---------|
| ![电站列表](./btoIMG/image-20230628113457160.png) | ![电站详情](./btoIMG/image-20230628114816142.png) |

#### 设备管理
| 逆变器列表 | 逆变器详情 |
|---------|---------|
| ![逆变器列表](./btoIMG/image-20230628113554502.png) | ![逆变器详情](./btoIMG/image-20230628115145555.png) |

| 运维器列表 | 设备监测 |
|---------|---------|
| ![运维器列表](./btoIMG/image-20230628113618958.png) | ![设备监测](./btoIMG/image-20230628115526104.png) |

#### 告警管理
| 报警列表 | 自检提示 | 运维器事件 |
|---------|---------|---------|
| ![报警列表](./btoIMG/image-20230628115610083.png) | ![自检提示](./btoIMG/image-20230628115911324.png) | ![运维器事件](./btoIMG/image-20230628120054292.png) |

#### 统计报表
| 能效收益统计 | 电站统计 | 日常统计 |
|---------|---------|---------|
| ![能效收益](./btoIMG/image-20230628120429096.png) | ![电站统计](./btoIMG/image-20230628141951811.png) | ![日常统计](./btoIMG/image-20230628142148233.png) |

#### 权限管理
| 用户管理 | 角色管理 | 菜单管理 |
|---------|---------|---------|
| ![用户管理](./btoIMG/image-20230628142410603.png) | ![角色管理](./btoIMG/image-20230628142429500.png) | ![菜单管理](./btoIMG/image-20230628142455753.png) |

#### 数据分析
| 发电量运维 | 故障诊断 | 预测分析 |
|---------|---------|---------|
| ![发电量运维](./btoIMG/image-20230628143007514.png) | ![故障诊断](./btoIMG/image-20230628143316524.png) | ![预测分析](./btoIMG/image-20230628143459403.png) |

### 移动端 APP

#### 工程师应用
| 登录首页 | 电站列表 | 设备详情 |
|---------|---------|---------|
| ![APP登录](./btoIMG/image-20230628144742430.png) | ![APP首页](./btoIMG/image-20230628144749443.png) | ![电站列表](./btoIMG/image-20230628144756750.png) |

| 运维器详情 | 故障信息 | 数据图表 |
|---------|---------|---------|
| ![运维器详情](./btoIMG/image-20230628144802176.png) | ![故障信息](./btoIMG/image-20230628144808513.png) | ![数据图表](./btoIMG/image-20230628144847771.png) |

| 电站编辑 | 地图位置 | 告警管理 |
|---------|---------|---------|
| ![电站编辑](./btoIMG/image-20230628144919235.png) | ![地图位置](./btoIMG/image-20230628145636834.png) | ![告警管理](./btoIMG/image-20230628145950118.png) |

## 🔧 快速开始

### 1. 环境准备

```bash
# 检查 Java 版本
java -version

# 检查 Maven 版本
mvn -version

# 启动 MySQL 服务
systemctl start mysql

# 启动 Redis 服务
systemctl start redis

# 启动 Nacos 服务
sh startup.sh -m standalone
```

### 2. 项目构建

```bash
# 克隆项目
git clone https://github.com/your-org/bto-solarman-parent.git

# 进入项目目录
cd bto-solarman-parent

# 编译项目
mvn clean compile

# 打包项目
mvn clean package -DskipTests
```

### 3. 配置文件

修改各服务的 `bootstrap.yml` 配置文件：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848  # Nacos 地址
      config:
        server-addr: 127.0.0.1:8848  # Nacos 配置中心地址
```

### 4. 启动服务

```bash
# 启动网关服务
cd bto-solarman-gateway
mvn spring-boot:run

# 启动系统服务
cd bto-solarman-system
mvn spring-boot:run

# 启动其他业务服务...
```

### 5. 访问系统

- **API 网关**：http://localhost:41000
- **API 文档**：http://localhost:41000/doc.html
- **Nacos 控制台**：http://localhost:8848/nacos

## 🔍 监控运维

### 应用监控

系统集成了 Spring Boot Admin 进行应用监控：

- **监控地址**：http://localhost:40000
- **功能特性**：
  - 应用健康状态监控
  - JVM 内存监控
  - 线程池监控
  - HTTP 请求追踪
  - 日志实时查看

### 链路追踪

支持 SkyWalking 分布式链路追踪：

- 服务调用链路追踪
- 性能瓶颈分析
- 异常定位
- 服务依赖关系图

### 日志管理

- **日志框架**：Log4j2
- **日志级别**：DEBUG、INFO、WARN、ERROR
- **日志输出**：控制台 + 文件
- **日志路径**：`logs/{service-name}/`

## 🛡️ 安全特性

### 认证授权

- **OAuth2**：基于 OAuth2 的认证授权机制
- **JWT Token**：无状态的 Token 认证
- **权限控制**：基于 RBAC 的权限模型
- **数据权限**：支持按组织架构的数据权限控制

### 安全防护

- **接口限流**：基于 Sentinel 的接口限流
- **参数校验**：统一的参数校验机制
- **SQL 注入防护**：MyBatis 预编译语句
- **XSS 防护**：输入输出过滤

## 📈 性能优化

### 缓存策略

- **Redis 缓存**：热点数据缓存
- **本地缓存**：Caffeine 本地缓存
- **缓存更新**：缓存与数据库双写一致性

### 数据库优化

- **读写分离**：主从数据库读写分离
- **分库分表**：大数据量表分片存储
- **索引优化**：合理的数据库索引设计
- **连接池**：Druid 连接池优化

### 服务优化

- **异步处理**：耗时操作异步化
- **批量操作**：减少数据库交互次数
- **懒加载**：按需加载数据
- **压缩传输**：HTTP 响应压缩

## 🧪 测试策略

### 单元测试

- **测试框架**：JUnit 5 + Mockito
- **覆盖率要求**：核心业务逻辑 > 80%
- **测试分类**：单元测试、集成测试、端到端测试

### 接口测试

- **API 测试**：Postman + Newman
- **自动化测试**：集成到 CI/CD 流程
- **性能测试**：JMeter 压力测试

## 📋 版本历史

### v3.0.0 (当前版本)
- 🚀 全新微服务架构重构
- 🚀 新增工程师移动端应用
- 🚀 增强数据分析功能
- 🚀 优化系统性能和稳定性
- 🚀 完善监控和运维体系

### v2.0.0
- ⭐️ 基础功能模块实现
- ⭐️ 电站管理、设备监控
- ⭐️ 告警统计、报表分析
- ⭐️ 权限管理系统

### v1.0.0
- 📦 项目初始版本
- 📦 基础架构搭建

## 🤝 贡献指南

### 开发流程

1. **Fork 项目**：从主仓库 Fork 到个人仓库
2. **创建分支**：基于 develop 分支创建功能分支
3. **开发功能**：按照开发规范进行功能开发
4. **提交代码**：提交代码并推送到个人仓库
5. **创建 PR**：向主仓库提交 Pull Request
6. **代码审查**：等待代码审查和合并

### 代码规范

- **命名规范**：遵循 Java 命名约定
- **注释规范**：关键方法和类必须有注释
- **格式规范**：使用统一的代码格式化配置
- **提交规范**：遵循 Conventional Commits 规范

### 分支管理

- **master**：生产环境分支
- **develop**：开发环境分支
- **feature/**：功能开发分支
- **hotfix/**：紧急修复分支
- **release/**：版本发布分支

## 📞 技术支持

### 联系方式

- **公司**：广东博通新能源科技有限公司
- **官网**：https://www.btosolarman.com
- **项目地址**：https://www.btosolarman.com/btosolar

### 技术交流

- **Issue 反馈**：通过 GitHub Issues 反馈问题
- **功能建议**：欢迎提出功能改进建议
- **技术讨论**：参与项目技术讨论

### 文档资源

- **API 文档**：http://localhost:41000/doc.html
- **开发文档**：详见项目 Wiki
- **部署文档**：详见部署指南

---

<div align="center">

**博通新能源光伏运维平台 3.0** - 让光伏运维更智能

[![Star](https://img.shields.io/github/stars/your-org/bto-solarman-parent?style=social)](https://github.com/your-org/bto-solarman-parent)
[![Fork](https://img.shields.io/github/forks/your-org/bto-solarman-parent?style=social)](https://github.com/your-org/bto-solarman-parent)

</div>
