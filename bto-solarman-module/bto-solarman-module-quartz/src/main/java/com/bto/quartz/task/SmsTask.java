package com.bto.quartz.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.IotCardQuery;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.ClientInfo;
import com.bto.commons.pojo.vo.IotCardInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.TokenHolder;
import com.bto.redis.utils.RedisUtil;
import com.bto.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bto.commons.constant.OauthConstants.*;
import static com.bto.commons.constant.TimingTimeConstant.*;

/**
 * 短信发送定时任务
 * 定时检查物联网卡流量使用情况，对即将到期的卡片发送预警短信
 * 支持批量短信发送和分批次处理，确保系统性能和稳定性
 * 
 * <AUTHOR>
 * @date 2023/11/10 17:52
 */
@Slf4j
@Service
public class SmsTask {

    @Autowired
    private SmsService smsService;
    @Autowired
    private DeviceServiceClient deviceServiceClient;
    @Autowired
    private SystemServiceClient systemServiceClient;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 执行短信预警任务
     * 定期检查物联网卡流量使用情况，对剩余流量即将到期的卡片发送预警短信
     * 支持分批次处理，避免大批量短信发送造成的系统压力
     *
     * @param params 任务执行参数，可用于指定检查周期或其他配置信息
     */
    public void run(String params) {
        log.info("开始执行短信预警任务，参数: {}", params);

        // 使用try-finally确保TokenHolder状态正确恢复
        boolean originalTokenValidationFlag = TokenHolder.getTokenValidationFlag();
        try {
            TokenHolder.setTokenValidationFlag(false);

            // 获取访问令牌
            String accessToken = getAccessToken();
            if (StrUtil.isBlank(accessToken)) {
                log.error("获取访问令牌失败，任务终止");
                return;
            }

            // 查询物联网卡信息
            List<IotCardInfoVO> iotCardList = getIotCardList(accessToken);
            if (CollUtil.isEmpty(iotCardList)) {
                log.info("未查询到符合条件的物联网卡信息");
                return;
            }

            // 筛选符合条件的物联网卡
            List<IotCardInfoVO> filterList = filterValidCards(iotCardList);
            if (CollUtil.isEmpty(filterList)) {
                log.info("未找到需要发送预警短信的物联网卡");
                return;
            }

            log.info("找到 {} 张需要发送预警短信的物联网卡", filterList.size());

            // 批量发送短信
            sendBatchSms(filterList);

            log.info("短信预警任务执行完成");

        } catch (Exception e) {
            log.error("短信预警任务执行失败", e);
            throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED, "短信预警任务执行失败: " + e.getMessage());
        } finally {
            // 恢复原始的TokenHolder状态
            TokenHolder.setTokenValidationFlag(originalTokenValidationFlag);
        }
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌，获取失败返回null
     */
    private String getAccessToken() {
        try {
            // 从Redis获取缓存的访问令牌
            String accessToken = (String) redisUtil.get("bto_admin");
            if (StrUtil.isNotBlank(accessToken)) {
                log.debug("从Redis获取到访问令牌");
                return accessToken;
            }

            // 缓存中没有令牌，重新获取
            log.info("Redis中未找到访问令牌，重新获取");
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("获取访问令牌时发生异常", e);
            return null;
        }
    }

    /**
     * 刷新访问令牌
     *
     * @return 新的访问令牌，获取失败返回null
     */
    private String refreshAccessToken() {
        try {
            ClientInfo client = new ClientInfo();
            client.setClientId("admin");
            client.setClientSecret("$2a$10$4XzCJPuzfBQHNocchVztDe2WY0Wy7V0EFoEC8.pQBYC0rBCEMuhva");
            client.setScope("all");
            client.setGrantType(CLIENT_CREDENTIALS);

            Object userLoginInfo = systemServiceClient.getAccessToken(client);
            if (userLoginInfo == null) {
                log.error("系统服务返回空的登录信息");
                return null;
            }

            Result userResult = JSONObject.parseObject(JSON.toJSONString(userLoginInfo), Result.class);
            if (userResult == null || userResult.getData() == null) {
                log.error("解析登录结果失败或数据为空");
                return null;
            }

            if (!ResultEnum.SUCCESS.getCode().equals(userResult.getStatus())) {
                log.error("获取访问令牌失败，状态码: {}, 消息: {}", userResult.getStatus(), userResult.getMessage());
                return null;
            }

            String accessToken = (String) BeanUtil.beanToMap(userResult.getData()).get(TOKEN_OF_ACCESS);
            if (StrUtil.isBlank(accessToken)) {
                log.error("从登录结果中提取访问令牌失败");
                return null;
            }

            log.info("成功获取新的访问令牌");
            return accessToken;

        } catch (Exception e) {
            log.error("刷新访问令牌时发生异常", e);
            return null;
        }
    }


    /**
     * 获取物联网卡列表
     *
     * @param accessToken 访问令牌
     * @return 物联网卡列表，获取失败返回空列表
     */
    private List<IotCardInfoVO> getIotCardList(String accessToken) {
        try {
            IotCardQuery query = new IotCardQuery();
            query.setPageSize(-1);
            query.setCurrentPage(-1);
            query.setRemainingDays(String.valueOf(THIRTY_DAYS));

            Result result = deviceServiceClient.getIotCardByPage(query, TOKEN_TYPE_BEARER + " " + accessToken);
            if (result == null) {
                log.error("设备服务返回空结果");
                return new ArrayList<>();
            }

            if (!ResultEnum.SUCCESS.getCode().equals(result.getStatus())) {
                log.error("查询物联网卡信息失败，状态码: {}, 消息: {}", result.getStatus(), result.getMessage());
                return new ArrayList<>();
            }

            if (result.getData() == null) {
                log.warn("查询结果数据为空");
                return new ArrayList<>();
            }

            TokenHolder.setTokenValidationFlag(true);

            IPage page = JSONObject.parseObject(JSONObject.toJSONString(result.getData()), Page.class);
            if (page == null || page.getRecords() == null) {
                log.warn("分页数据为空");
                return new ArrayList<>();
            }

            String jsonString = JSONObject.toJSONString(page.getRecords());
            List<IotCardInfoVO> iotCardList = JSONUtil.toList(jsonString, IotCardInfoVO.class);

            log.info("成功查询到 {} 条物联网卡信息", iotCardList != null ? iotCardList.size() : 0);
            return iotCardList != null ? iotCardList : new ArrayList<>();

        } catch (Exception e) {
            log.error("查询物联网卡信息时发生异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 筛选符合条件的物联网卡
     *
     * @param iotCardList 原始物联网卡列表
     * @return 筛选后的物联网卡列表
     */
    private List<IotCardInfoVO> filterValidCards(List<IotCardInfoVO> iotCardList) {
        if (CollUtil.isEmpty(iotCardList)) {
            return new ArrayList<>();
        }

        return iotCardList.stream()
                .filter(Objects::nonNull)
                .filter(iotCardInfoVO -> {
                    Integer remainingDays = iotCardInfoVO.getRemainingDays();
                    if (remainingDays == null) {
                        log.warn("物联网卡剩余天数为空，跳过: {}", iotCardInfoVO.getPhoneNumber());
                        return false;
                    }

                    boolean isMatch = remainingDays.equals(ZERO_DAY) ||
                                    remainingDays.equals(SEVEN_DAYS) ||
                                    remainingDays.equals(FIFTEEN_DAYS) ||
                                    remainingDays.equals(THIRTY_DAYS);

                    if (isMatch) {
                        log.debug("物联网卡 {} 剩余天数 {} 符合预警条件", iotCardInfoVO.getPhoneNumber(), remainingDays);
                    }

                    return isMatch;
                })
                .filter(iotCardInfoVO -> {
                    String phoneNumber = iotCardInfoVO.getPhoneNumber();
                    if (StrUtil.isBlank(phoneNumber)) {
                        log.warn("物联网卡电话号码为空，跳过");
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 批量发送短信
     *
     * @param filterList 需要发送短信的物联网卡列表
     */
    private void sendBatchSms(List<IotCardInfoVO> filterList) {
        try {
            // 提取电话号码并转化成号码列表
            List<String> phoneNumbers = filterList.stream()
                    .map(IotCardInfoVO::getPhoneNumber)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

            List<SmsInfoDTO> smsInfoList = BeanUtil.copyToList(filterList, SmsInfoDTO.class);

            if (CollUtil.isEmpty(phoneNumbers) || CollUtil.isEmpty(smsInfoList)) {
                log.warn("电话号码列表或短信信息列表为空，无法发送短信");
                return;
            }

            // 分批处理，每批100条
            List<List<String>> splitPhones = ListUtil.split(phoneNumbers, 100);
            List<List<SmsInfoDTO>> splitSmsInfo = ListUtil.split(smsInfoList, 100);

            log.info("开始分批发送短信，共 {} 批", splitPhones.size());

            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < splitPhones.size(); i++) {
                List<String> batchPhones = splitPhones.get(i);
                List<SmsInfoDTO> batchSmsInfo = splitSmsInfo.get(i);

                if (CollUtil.isEmpty(batchPhones) || CollUtil.isEmpty(batchSmsInfo)) {
                    log.warn("第 {} 批数据为空，跳过", i + 1);
                    continue;
                }

                try {
                    log.info("正在发送第 {} 批短信，数量: {}", i + 1, batchPhones.size());
                    smsService.batchSendAlarmMsg(batchPhones, batchSmsInfo);
                    successCount += batchPhones.size();
                    log.info("第 {} 批短信发送成功", i + 1);

                } catch (ExecutionException e) {
                    failCount += batchPhones.size();
                    log.error("第 {} 批短信发送失败 - ExecutionException", i + 1, e);
                    // 继续处理下一批，不中断整个任务

                } catch (InterruptedException e) {
                    failCount += batchPhones.size();
                    log.error("第 {} 批短信发送被中断 - InterruptedException", i + 1, e);
                    // 恢复中断状态
                    Thread.currentThread().interrupt();
                    // 中断异常应该停止后续处理
                    break;

                } catch (Exception e) {
                    failCount += batchPhones.size();
                    log.error("第 {} 批短信发送失败 - 未知异常", i + 1, e);
                    // 继续处理下一批
                }
            }

            log.info("短信发送完成，成功: {} 条，失败: {} 条", successCount, failCount);

        } catch (Exception e) {
            log.error("批量发送短信时发生异常", e);
            throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED, "批量发送短信失败: " + e.getMessage());
        }
    }

}
