package com.bto.quartz.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.IotCardQuery;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.ClientInfo;
import com.bto.commons.pojo.vo.IotCardInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.TokenHolder;
import com.bto.redis.utils.RedisUtil;
import com.bto.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bto.commons.constant.OauthConstants.*;
import static com.bto.commons.constant.TimingTimeConstant.*;

/**
 * 短信发送定时任务
 * 定时检查物联网卡流量使用情况，对即将到期的卡片发送预警短信
 * 支持批量短信发送和分批次处理，确保系统性能和稳定性
 * 
 * <AUTHOR>
 * @date 2023/11/10 17:52
 */
@Slf4j
@Service
public class SmsTask {

    @Autowired
    private SmsService smsService;
    @Autowired
    private DeviceServiceClient deviceServiceClient;
    @Autowired
    private SystemServiceClient systemServiceClient;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 执行短信预警任务
     * 定期检查物联网卡流量使用情况，对剩余流量即将到期的卡片发送预警短信
     * 支持分批次处理，避免大批量短信发送造成的系统压力
     * 
     * @param params 任务执行参数，可用于指定检查周期或其他配置信息
     */
    public void run(String params) {
        IotCardQuery query = new IotCardQuery();
        query.setPageSize(-1);
        query.setCurrentPage(-1);
        query.setRemainingDays(String.valueOf(THIRTY_DAYS));
        //查询所有剩余天数小于60天的所有物联网卡信息数据
        TokenHolder.setTokenValidationFlag(false);
        String accessToken = (String) redisUtil.get("bto_admin");
        if (accessToken == null) {
            ClientInfo client = new ClientInfo();
            client.setClientId("admin");
            client.setClientSecret("$2a$10$4XzCJPuzfBQHNocchVztDe2WY0Wy7V0EFoEC8.pQBYC0rBCEMuhva");
            client.setScope("all");
            client.setGrantType(CLIENT_CREDENTIALS);
            Object userLoginInfo = systemServiceClient.getAccessToken(client);
            Result userResult = JSONObject.parseObject(JSON.toJSONString(userLoginInfo), Result.class);
            accessToken = (String) BeanUtil.beanToMap(userResult.getData()).get(TOKEN_OF_ACCESS);
        }
        Result result = deviceServiceClient.getIotCardByPage(query, TOKEN_TYPE_BEARER + " " + accessToken);
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            TokenHolder.setTokenValidationFlag(true);
            IPage page = JSONObject.parseObject(JSONObject.toJSONString(result.getData()), Page.class);
            String jsonString = JSONObject.toJSONString(page.getRecords());
            List<IotCardInfoVO> iotCardList = JSONUtil.toList(jsonString, IotCardInfoVO.class);
            //筛选出符合条件的物联网卡信息列表
            List<IotCardInfoVO> filterList = iotCardList.stream().filter(
                    iotCardInfoVO -> {
                        Integer remainingDays = iotCardInfoVO.getRemainingDays();
                        return remainingDays.equals(ZERO_DAY) || remainingDays.equals(SEVEN_DAYS) || remainingDays.equals(FIFTEEN_DAYS) || remainingDays.equals(THIRTY_DAYS);
                    }
            ).collect(Collectors.toList());
            //提取电话号码并转化成号码列表
            List<String> phoneNumbers = filterList.stream().map(IotCardInfoVO::getPhoneNumber).collect(Collectors.toList());
            List<SmsInfoDTO> smsInfoList = BeanUtil.copyToList(filterList, SmsInfoDTO.class);
            List<List<String>> splitPhones = ListUtil.split(phoneNumbers, 100);
            List<List<SmsInfoDTO>> splitSmsInfo = ListUtil.split(smsInfoList, 100);
            for (int i = 0; i < splitPhones.size(); i++) {
                try {
                    smsService.batchSendAlarmMsg(splitPhones.get(i),splitSmsInfo.get(i));
                } catch (ExecutionException e) {
                    e.printStackTrace();
                    throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED);
                }
            }
            log.info(params);
        }
    }


}
