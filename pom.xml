<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bto</groupId>
    <artifactId>bto-solarman-parent</artifactId>
    <version>3.0.0</version>

    <packaging>pom</packaging>
    <modules>
        <module>bto-solarman-gateway</module>
        <module>bto-solarman-common</module>
        <module>bto-solarman-plant-manage</module>
        <module>bto-solarman-device-manage</module>
        <module>bto-solarman-system</module>
        <module>bto-solarman-alarm-manage</module>
        <module>bto-solarman-statistics</module>
        <module>bto-solarman-monitor</module>
        <module>bto-solarman-module</module>
        <module>bto-engineer-app</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.cloud-version>Hoxton.SR9</spring.cloud-version>
        <spring-boot.version>2.3.5.RELEASE</spring-boot.version>
        <spring-cloud-alibaba.version>2.2.0.RELEASE</spring-cloud-alibaba.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <knife4j.version>2.0.8</knife4j.version>
        <springfox.version>3.0.0</springfox.version>
        <hutool.version>5.8.18</hutool.version>
        <pagehelper.version>1.4.6</pagehelper.version>
        <hystrix.version>2.2.9.RELEASE</hystrix.version>
        <spring-boot-admin.version>2.3.1</spring-boot-admin.version>
        <easy-trans.version>2.2.5</easy-trans.version>
        <aliyun.oss.version>3.15.2</aliyun.oss.version>
        <aliyun.dysmsapi.version>2.0.23</aliyun.dysmsapi.version>
        <tencentcloud.sdk.version>3.1.574</tencentcloud.sdk.version>
        <qiniu.version>7.12.1</qiniu.version>
        <minio.version>8.5.1</minio.version>
        <qcloud.cos.version>5.6.97</qcloud.cos.version>
        <huaweicloud.obs.version>3.22.3</huaweicloud.obs.version>
        <dynamic-datasource-spring-boot-starter.version>4.3.0</dynamic-datasource-spring-boot-starter.version>
        <guava.version>31.1-jre</guava.version>
    </properties>

    <dependencyManagement>
        <dependencies>


            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>
            <!--引入spring-cloud Hoxton.SR9依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!--引入spring-boot 2.3.5.RELEASE依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--引入spring-cloud-alibaba 2.2.0.RELEASE依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-aggregation-spring-boot-starter</artifactId>
                <!--在引用时请在maven中央仓库搜索aggregation最新版本号-->
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-springdoc-ui</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- MyBatis 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <!-- 引入spring-boot-admin依赖 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.dysmsapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${qcloud.cos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java-bundle</artifactId>
                <version>${huaweicloud.obs.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <skipTests>true</skipTests>    <!--默认关掉单元测试 -->
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>