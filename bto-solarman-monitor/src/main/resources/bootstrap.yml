server:
  port: 40000
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        service: solarman-monitor
        namespace: light-cloud
  application:
    name: solarman-monitor
  boot:
    admin:
      client:
        api-path: instances
        url: http://127.0.0.1:41000/monitor/
        instance:
          prefer-ip: true
management:
  endpoint:
    loggers:
      enabled: true
    health:
      show-details: always
    logfile:
      external-file: logs/${spring.application.name}/error.log
  endpoints:
    web:
      exposure:
        include: "*"